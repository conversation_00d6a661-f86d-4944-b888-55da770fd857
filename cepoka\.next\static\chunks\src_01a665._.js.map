{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/lib/appwrite.ts"], "sourcesContent": ["import { Client, Account, Databases, Storage } from \"appwrite\";\r\n\r\n// Initialize the Appwrite client\r\nconst client = new Client()\r\n  .setEndpoint(\"https://cloud.appwrite.io/v1\")\r\n  .setProject(\"67d07dc9000bafdd5d81\"); // Confirmed correct project ID\r\n\r\nexport const account = new Account(client);\r\nexport const databases = new Databases(client);\r\nexport const storage = new Storage(client);\r\n\r\nexport const appwriteConfig = {\r\n  // Using the confirmed database ID\r\n  databaseId: \"6813eadb003e7d64f63c\",\r\n  productsCollectionId: \"6813eaf40036e52c29b1\",\r\n  categoriesCollectionId: \"6817640f000dd0b67c77\",\r\n  stockProductsCollectionId: \"681a651d001cc3de8395\",\r\n  stockMovementsCollectionId: \"681bddcc000204a3748d\",\r\n  storageId: \"6813ea36001624c1202a\",\r\n};\r\n\r\n// project id: 67d07d7b0010f39ec77d\r\n// database id: 67d8833d000778157021\r\n// collection id: 67d8835b002502c5d7ba\r\n// storage id: 67d8841a001213adf116\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,iCAAiC;AACjC,MAAM,SAAS,IAAI,iJAAA,CAAA,SAAM,GACtB,WAAW,CAAC,gCACZ,UAAU,CAAC,yBAAyB,+BAA+B;AAE/D,MAAM,UAAU,IAAI,iJAAA,CAAA,UAAO,CAAC;AAC5B,MAAM,YAAY,IAAI,iJAAA,CAAA,YAAS,CAAC;AAChC,MAAM,UAAU,IAAI,iJAAA,CAAA,UAAO,CAAC;AAE5B,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,YAAY;IACZ,sBAAsB;IACtB,wBAAwB;IACxB,2BAA2B;IAC3B,4BAA4B;IAC5B,WAAW;AACb,GAEA,mCAAmC;CACnC,oCAAoC;CACpC,sCAAsC;CACtC,mCAAmC"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/SpinningLoader.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface SpinningLoaderProps {\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  text?: string;\n}\n\nconst SpinningLoader: React.FC<SpinningLoaderProps> = ({ \n  size = 'medium', \n  className = '',\n  text\n}) => {\n  // Size mapping\n  const sizeMap = {\n    small: 'w-6 h-6 border-2',\n    medium: 'w-10 h-10 border-3',\n    large: 'w-16 h-16 border-4',\n  };\n\n  const sizeClass = sizeMap[size];\n  \n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <motion.div\n        className={`${sizeClass} rounded-full border-t-pink-500 border-r-blue-500 border-b-pink-500 border-l-blue-500`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1.5,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n        style={{ borderStyle: 'solid' }}\n      />\n      {text && (\n        <p className=\"mt-3 text-sm text-gray-600 font-medium\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default SpinningLoader;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,IAAI,EACL;IACC,eAAe;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,CAAC,KAAK;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,UAAU,qFAAqF,CAAC;gBAC9G,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,aAAa;gBAAQ;;;;;;YAE/B,sBACC,6LAAC;gBAAE,WAAU;0BAA0C;;;;;;;;;;;;AAI/D;KA/BM;uCAiCS"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport SpinningLoader from './SpinningLoader';\n\ninterface LoadingScreenProps {\n  message?: string;\n  isFullScreen?: boolean;\n}\n\nexport default function LoadingScreen({ \n  message = 'Loading...', \n  isFullScreen = true \n}: LoadingScreenProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className={`flex flex-col items-center justify-center bg-white bg-opacity-90 z-[9999] ${\n        isFullScreen ? 'fixed inset-0' : 'absolute inset-0'\n      }`}\n    >\n      <div className=\"text-center p-6 rounded-lg\">\n        <SpinningLoader size=\"large\" />\n        <p className=\"mt-4 text-gray-700 font-medium text-lg\">{message}</p>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AADA;AAFA;;;;AAUe,SAAS,cAAc,EACpC,UAAU,YAAY,EACtB,eAAe,IAAI,EACA;IACnB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,WAAW,CAAC,0EAA0E,EACpF,eAAe,kBAAkB,oBACjC;kBAEF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,8IAAA,CAAA,UAAc;oBAAC,MAAK;;;;;;8BACrB,6LAAC;oBAAE,WAAU;8BAA0C;;;;;;;;;;;;;;;;;AAI/D;KAnBwB"}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/data/categories.ts"], "sourcesContent": ["export interface Category {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  imageSrc: string;\r\n}\r\n\r\nexport const CATEGORIES: Category[] = [\r\n  {\r\n    id: \"spa-salon-furniture\",\r\n    name: \"Spa and salon furnitures\",\r\n    icon: \"🪑\",\r\n    imageSrc: \"/icons/spa-bed.png\",\r\n  },\r\n  {\r\n    id: \"beauty-equipment\",\r\n    name: \"Beauty equipment\",\r\n    icon: \"⚙️\",\r\n    imageSrc: \"/icons/hairdryer.png\",\r\n  },\r\n  {\r\n    id: \"facial-waxing\",\r\n    name: \"Facials and waxing\",\r\n    icon: \"🧖‍♀️\",\r\n    imageSrc: \"/icons/hot-stone.png\",\r\n  },\r\n  {\r\n    id: \"skincare-accessories\",\r\n    name: \"Skincare products & accessories\",\r\n    icon: \"🧴\",\r\n    imageSrc: \"/icons/slim.png\",\r\n  },\r\n  {\r\n    id: \"pedicure-manicure\",\r\n    name: \"Pedicure and manicure\",\r\n    icon: \"💅\",\r\n    imageSrc: \"/icons/nails.png\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AAOO,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;CACD"}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/admin/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from 'react';\r\nimport { databases, storage, appwriteConfig } from '../../lib/appwrite';\r\nimport { ID, Query } from 'appwrite';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\nimport toast from 'react-hot-toast';\r\n// import { useRouter } from 'next/navigation';\r\nimport { motion, AnimatePresence, type Variants } from 'framer-motion';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport LoadingScreen from '../Components/LoadingScreen';\r\nimport SpinningLoader from '../Components/SpinningLoader';\r\nimport { CATEGORIES } from '@/src/data/categories'\r\n// Category services are not used in this component\r\n\r\n\r\n// Schema for product form validation using Zod\r\n// Get valid category IDs from CATEGORIES\r\nconst validCategoryIds = CATEGORIES.map(cat => cat.id);\r\n\r\nconst productSchema = z.object({\r\n    name: z.string().min(1, \"Product name is required\"),\r\n    price: z.string().min(1, \"Price is required\"),\r\n    description: z.string().min(1, \"Description is required\"),\r\n    category: z.string().refine(val => validCategoryIds.includes(val), {\r\n        message: \"Please select a valid category\"\r\n    }).optional(),\r\n});\r\n\r\n// Type definition for Appwrite error responses\r\ntype AppwriteError = {\r\n    message: string;\r\n    code: number;\r\n};\r\n\r\n// Interface for product data structure\r\ninterface Product {\r\n    $id: string;          // Unique identifier from Appwrite\r\n    name: string;         // Product name\r\n    price: string;        // Product price\r\n    description: string;  // Product description\r\n    imageUrls: string[]; // Array of image URLs\r\n    category?: string;   // Category ID from predefined CATEGORIES\r\n}\r\n\r\n// Interface for\r\ninterface ProductFormData {\r\n    name: string;\r\n    price: string;\r\n    description: string;\r\n    category?: string;\r\n}\r\n\r\nconst AdminPage = () => {\r\n    // Router for navigation is not used in this component\r\n\r\n    // State management\r\n    const [isAuthorized, setIsAuthorized] = useState(false);        // Authorization status\r\n    const [products, setProducts] = useState<Product[]>([]);         // List of products\r\n    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);  // Selected image files\r\n    const [existingImageUrls, setExistingImageUrls] = useState<string[]>([]); // Track existing image URLs\r\n    const [isLoading, setIsLoading] = useState(false);              // Loading state\r\n    const [isNavigating] = useState(false);        // Navigation loading state\r\n    const [isStockNavigating] = useState(false);        // Navigation loading state\r\n    const [categoriesLoading, setCategoriesLoading] = useState(true); // Categories loading state\r\n    const [editingProduct, setEditingProduct] = useState<Product | null>(null); // Currently editing product\r\n    const [showImageModal, setShowImageModal] = useState<string | null>(null);  // Image modal visibility\r\n    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');        // Sort order for products\r\n    const [currentImageIndex, setCurrentImageIndex] = useState(0);             // Current image index in modal\r\n    const [showDeleteModal, setShowDeleteModal] = useState<string | null>(null); // Delete confirmation modal\r\n    const [selectedCategory, setSelectedCategory] = useState<string | null>(null); // Selected category from predefined CATEGORIES\r\n    const [isDragging, setIsDragging] = useState(false); // State for drag and drop functionality\r\n    const fileInputRef = useRef<HTMLInputElement>(null); // Reference to file input element\r\n    const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false); // Bulk delete confirmation modal\r\n    const [isBulkDeleting, setIsBulkDeleting] = useState(false); // Bulk delete loading state\r\n    const [bulkDeleteProgress, setBulkDeleteProgress] = useState({ current: 0, total: 0 }); // Bulk delete progress\r\n    const [selectedProducts, setSelectedProducts] = useState<string[]>([]); // Selected products for multi-delete\r\n    const [isMultiSelectMode, setIsMultiSelectMode] = useState(false); // Multi-select mode toggle\r\n    const [showCategoryDeleteModal, setShowCategoryDeleteModal] = useState<string | null>(null); // Category delete confirmation modal\r\n\r\n    // Ref for form scrolling\r\n    const formRef = useRef<HTMLDivElement>(null);\r\n\r\n    // Form setup using react-hook-form with Zod validation\r\n    const { register, handleSubmit, reset, formState: { errors }, getValues } = useForm<ProductFormData>({\r\n        resolver: zodResolver(productSchema)\r\n    });\r\n\r\n    // Function to fetch products - fetch ALL products with pagination\r\n    const fetchProducts = useCallback(async () => {\r\n        try {\r\n            // First request with limit=100 (Appwrite's maximum)\r\n            const response = await databases.listDocuments(\r\n                appwriteConfig.databaseId,\r\n                appwriteConfig.productsCollectionId,\r\n                [\r\n                    Query.limit(100), // Get 100 documents per request (maximum)\r\n                    Query.offset(0),  // Start from the first document\r\n                    sortOrder === 'asc' ? Query.orderAsc('$createdAt') : Query.orderDesc('$createdAt')\r\n                ]\r\n            );\r\n\r\n            // Initialize our products array with the first batch\r\n            let allDocuments = [...response.documents];\r\n\r\n            // If there are more documents than the limit, fetch them with pagination\r\n            if (response.total > 100) {\r\n                // Calculate how many more requests we need\r\n                const totalRequests = Math.ceil(response.total / 100);\r\n\r\n                // Make additional requests to get all documents\r\n                for (let i = 1; i < totalRequests; i++) {\r\n                    const offset = i * 100;\r\n                    const additionalResponse = await databases.listDocuments(\r\n                        appwriteConfig.databaseId,\r\n                        appwriteConfig.productsCollectionId,\r\n                        [\r\n                            Query.limit(100),\r\n                            Query.offset(offset),\r\n                            sortOrder === 'asc' ? Query.orderAsc('$createdAt') : Query.orderDesc('$createdAt')\r\n                        ]\r\n                    );\r\n\r\n                    // Add these documents to our array\r\n                    allDocuments = [...allDocuments, ...additionalResponse.documents];\r\n                }\r\n            }\r\n\r\n            setProducts(allDocuments as unknown as Product[]);\r\n        } catch (error) {\r\n            console.error('Error fetching products:', error);\r\n            toast.error('Failed to fetch products');\r\n        }\r\n    }, [sortOrder]);\r\n\r\n    // Clear form function - used for both Done and Cancel\r\n    const clearForm = () => {\r\n        setEditingProduct(null);\r\n        setSelectedCategory(null);\r\n        setSelectedFiles([]);\r\n        setExistingImageUrls([]); // Clear existing image URLs\r\n        reset({\r\n            name: '',\r\n            price: '',\r\n            description: '',\r\n            category: ''\r\n        });\r\n        toast.success('Form cleared');\r\n    };\r\n\r\n    // Cancel edit function\r\n    const cancelEdit = () => {\r\n        clearForm();\r\n        toast.success('Edit cancelled');\r\n    };\r\n\r\n    // Fetch products from Appwrite database\r\n    useEffect(() => {\r\n        const fetchProductsInEffect = async () => {\r\n            try {\r\n                setIsLoading(true);\r\n                // First request with limit=100 (Appwrite's maximum)\r\n                const response = await databases.listDocuments(\r\n                    appwriteConfig.databaseId,\r\n                    appwriteConfig.productsCollectionId,\r\n                    [\r\n                        Query.limit(100), // Get 100 documents per request (maximum)\r\n                        Query.offset(0),  // Start from the first document\r\n                        sortOrder === 'asc' ? Query.orderAsc('$createdAt') : Query.orderDesc('$createdAt')\r\n                    ]\r\n                );\r\n\r\n                // Initialize our products array with the first batch\r\n                let allDocuments = [...response.documents];\r\n\r\n                // If there are more documents than the limit, fetch them with pagination\r\n                if (response.total > 100) {\r\n                    // Calculate how many more requests we need\r\n                    const totalRequests = Math.ceil(response.total / 100);\r\n\r\n                    // Make additional requests to get all documents\r\n                    for (let i = 1; i < totalRequests; i++) {\r\n                        const offset = i * 100;\r\n                        const additionalResponse = await databases.listDocuments(\r\n                            appwriteConfig.databaseId,\r\n                            appwriteConfig.productsCollectionId,\r\n                            [\r\n                                Query.limit(100),\r\n                                Query.offset(offset),\r\n                                sortOrder === 'asc' ? Query.orderAsc('$createdAt') : Query.orderDesc('$createdAt')\r\n                            ]\r\n                        );\r\n\r\n                        // Add these documents to our array\r\n                        allDocuments = [...allDocuments, ...additionalResponse.documents];\r\n                    }\r\n                }\r\n\r\n                setProducts(allDocuments as unknown as Product[]);\r\n            } catch (error) {\r\n                console.error('Error fetching products:', error);\r\n                toast.error('Failed to fetch products');\r\n            } finally {\r\n                setIsLoading(false);\r\n            }\r\n        };\r\n\r\n        if (isAuthorized) {\r\n            fetchProductsInEffect();\r\n        }\r\n    }, [isAuthorized, sortOrder]);    // No need to fetch categories since we're using fixed ones from CATEGORIES\r\n    useEffect(() => {\r\n        setCategoriesLoading(false);\r\n    }, []);\r\n\r\n    // Handle form submission for creating/updating products\r\n    const onSubmit = async (data: ProductFormData) => {\r\n        try {\r\n            setIsLoading(true);\r\n            console.log('📝 Submitting product data:', data);\r\n\r\n            // Handle image uploads - only upload new images\r\n            let imageUrls: string[] = [];\r\n\r\n            if (selectedFiles.length > 0) {\r\n                try {\r\n                    console.log('🖼️ Processing images...');\r\n\r\n                    // Separate existing images from new images\r\n                    const newFiles: File[] = [];\r\n                    const existingUrls: string[] = [];\r\n\r\n                    for (let i = 0; i < selectedFiles.length; i++) {\r\n                        const file = selectedFiles[i];\r\n                        const correspondingExistingUrl = existingImageUrls[i];\r\n\r\n                        // Check if this is an existing image (has a corresponding URL and filename matches pattern)\r\n                        if (correspondingExistingUrl && file.name.startsWith('existing-image-')) {\r\n                            // This is an existing image, keep the original URL\r\n                            existingUrls.push(correspondingExistingUrl);\r\n                            console.log(`📷 Keeping existing image: ${file.name}`);\r\n                        } else {\r\n                            // This is a new image, needs to be uploaded\r\n                            newFiles.push(file);\r\n                            console.log(`📷 New image to upload: ${file.name}`);\r\n                        }\r\n                    }\r\n\r\n                    // Upload only new files\r\n                    if (newFiles.length > 0) {\r\n                        console.log(`🖼️ Uploading ${newFiles.length} new images...`);\r\n                        const uploadPromises = newFiles.map(file =>\r\n                            storage.createFile(\r\n                                appwriteConfig.storageId,\r\n                                ID.unique(),\r\n                                file\r\n                            )\r\n                        );\r\n\r\n                        const uploadedFiles = await Promise.all(uploadPromises);\r\n                        // Generate URLs for uploaded images\r\n                        const newImageUrls = uploadedFiles.map(file => {\r\n                            if (file && file.$id) {\r\n                                // Use the getFileView method instead of constructing URL manually\r\n                                return storage.getFileView(\r\n                                    appwriteConfig.storageId,\r\n                                    file.$id\r\n                                ).toString();\r\n                            } else {\r\n                                console.error('❌ File upload error: missing file ID');\r\n                                toast.error('Failed to upload images. Please try again.');\r\n                                setIsLoading(false);\r\n                                return \"\";\r\n                            }\r\n                        });\r\n\r\n                        // Combine existing URLs with new URLs in the correct order\r\n                        imageUrls = [];\r\n                        let newUrlIndex = 0;\r\n                        let existingUrlIndex = 0;\r\n\r\n                        for (let i = 0; i < selectedFiles.length; i++) {\r\n                            const file = selectedFiles[i];\r\n                            const correspondingExistingUrl = existingImageUrls[i];\r\n\r\n                            if (correspondingExistingUrl && file.name.startsWith('existing-image-')) {\r\n                                // Use existing URL\r\n                                imageUrls.push(existingUrls[existingUrlIndex]);\r\n                                existingUrlIndex++;\r\n                            } else {\r\n                                // Use new URL\r\n                                imageUrls.push(newImageUrls[newUrlIndex]);\r\n                                newUrlIndex++;\r\n                            }\r\n                        }\r\n\r\n                        console.log('✅ Images processed successfully');\r\n                    } else {\r\n                        // All images are existing, just use the existing URLs\r\n                        imageUrls = existingUrls;\r\n                        console.log('✅ All images are existing, no upload needed');\r\n                    }\r\n                } catch (fileError: unknown) {\r\n                    const appwriteError = fileError as AppwriteError;\r\n                    console.error('❌ File upload error:', appwriteError);\r\n                    toast.error('Failed to upload images. Please try again.');\r\n                    setIsLoading(false);\r\n                    return;\r\n                }\r\n            }            // Update the productData object with the exact category ID and name from CATEGORIES\r\n            const category = CATEGORIES.find(cat => cat.id === selectedCategory);\r\n            if (!category) {\r\n                toast.error('Invalid category selected');\r\n                setIsLoading(false);\r\n                return;\r\n            }\r\n            const productData = {\r\n                name: data.name,\r\n                price: data.price,\r\n                description: data.description,\r\n                category: selectedCategory || editingProduct?.category || \"beauty-equipment\", // Ensure we have a valid category ID\r\n                imageUrls: imageUrls.length > 0 ? imageUrls : (editingProduct?.imageUrls || []),\r\n            };\r\n\r\n            console.log('📦 Final product data being sent to Appwrite:', productData);\r\n\r\n            if (editingProduct) {\r\n                try {\r\n                    console.log('📝 Updating existing product...');\r\n                    await databases.updateDocument(\r\n                        appwriteConfig.databaseId,\r\n                        appwriteConfig.productsCollectionId,\r\n                        editingProduct.$id,\r\n                        productData\r\n                    );\r\n                    console.log('✅ Product updated successfully');\r\n                    toast.success('Product updated successfully');\r\n                } catch (updateError: unknown) {\r\n                    const appwriteError = updateError as AppwriteError;\r\n                    console.error('❌ Product update error:', appwriteError);\r\n                    toast.error('Failed to update product. Please try again.');\r\n                    setIsLoading(false);\r\n                    return;\r\n                }\r\n            } else {\r\n                try {\r\n                    console.log('📝 Creating new product...');\r\n                    const newProduct = await databases.createDocument(\r\n                        appwriteConfig.databaseId,\r\n                        appwriteConfig.productsCollectionId,\r\n                        ID.unique(),\r\n                        productData\r\n                    );\r\n                    console.log('✅ Product created successfully:', newProduct);\r\n                    toast.success('Product created successfully');\r\n                    clearForm(); // Clear all fields after successful creation\r\n                } catch (createError: unknown) {\r\n                    const appwriteError = createError as AppwriteError;\r\n                    console.error('❌ Product creation error:', appwriteError);\r\n                    toast.error('Failed to create product. Please try again.');\r\n                    setIsLoading(false);\r\n                    return;\r\n                }\r\n            }\r\n\r\n            // Clear form and state after successful submission\r\n            clearForm();\r\n            await fetchProducts();\r\n        } catch (error: unknown) {\r\n            const appwriteError = error as AppwriteError;\r\n            console.error('❌ General error:', appwriteError);\r\n            toast.error('An error occurred. Please try again.');\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    // Handle file validation for image uploads\r\n    const validateFiles = (files: File[]): File[] => {\r\n        // Filter for image files only\r\n        const imageFiles = files.filter(file => file.type.startsWith('image/'));\r\n\r\n        // Check if any files were filtered out\r\n        if (imageFiles.length < files.length) {\r\n            toast.error('Only image files are allowed');\r\n        }\r\n\r\n        // Check file sizes (max 10MB)\r\n        const validSizeFiles = imageFiles.filter(file => {\r\n            const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB\r\n            if (!isValidSize) {\r\n                toast.error(`File \"${file.name}\" exceeds 10MB limit`);\r\n            }\r\n            return isValidSize;\r\n        });\r\n\r\n        // When adding new files, combine with existing files but respect the 3 image limit\r\n        const currentFiles = selectedFiles.filter(file => !file.name.startsWith('existing-image-') || existingImageUrls.length > 0);\r\n        const totalFiles = [...currentFiles, ...validSizeFiles].slice(0, 3);\r\n\r\n        // Show warning if files were truncated\r\n        if (currentFiles.length + validSizeFiles.length > 3) {\r\n            toast.error('Maximum 3 images allowed');\r\n        }\r\n\r\n        return totalFiles;\r\n    };\r\n\r\n    // Handle product deletion\r\n    const confirmDelete = async () => {\r\n        if (showDeleteModal) {\r\n            try {\r\n                await databases.deleteDocument(\r\n                    appwriteConfig.databaseId,\r\n                    appwriteConfig.productsCollectionId,\r\n                    showDeleteModal\r\n                );\r\n                toast.success('Product deleted successfully');\r\n                setShowDeleteModal(null);\r\n                await fetchProducts();\r\n            } catch (error) {\r\n                console.error('Error deleting product:', error);\r\n                toast.error('Failed to delete product');\r\n            }\r\n        }\r\n    };\r\n\r\n    const confirmCategoryDelete = async () => {\r\n        if (!showCategoryDeleteModal) return;\r\n\r\n        try {\r\n            // For now, just close the modal since we're using predefined categories\r\n            setShowCategoryDeleteModal(null);\r\n            toast.success('Category deleted successfully');\r\n        } catch (error) {\r\n            console.error('Error deleting category:', error);\r\n            toast.error('Failed to delete category');\r\n        }\r\n    };\r\n\r\n    const handleEdit = (product: Product) => {\r\n        setEditingProduct(product);\r\n\r\n        // Reset form with product data\r\n        reset({\r\n            name: product.name,\r\n            price: product.price,\r\n            description: product.description,\r\n            category: product.category\r\n        });\r\n\r\n        // Pre-select the product's category\r\n        setSelectedCategory(product.category || null);\r\n\r\n        // Load existing images as File objects for editing\r\n        if (product.imageUrls && product.imageUrls.length > 0) {\r\n            // Store the existing image URLs\r\n            setExistingImageUrls(product.imageUrls);\r\n\r\n            const loadExistingImages = async () => {\r\n                try {\r\n                    const imageFiles: File[] = [];\r\n\r\n                    for (let i = 0; i < product.imageUrls.length; i++) {\r\n                        const imageUrl = product.imageUrls[i];\r\n                        try {\r\n                            // Fetch the image and convert to File object\r\n                            const response = await fetch(imageUrl);\r\n                            const blob = await response.blob();\r\n                            const fileName = `existing-image-${i + 1}.${blob.type.split('/')[1] || 'jpg'}`;\r\n                            const file = new File([blob], fileName, { type: blob.type });\r\n                            imageFiles.push(file);\r\n                        } catch (imageError) {\r\n                            console.warn(`Failed to load image ${i + 1}:`, imageError);\r\n                        }\r\n                    }\r\n\r\n                    setSelectedFiles(imageFiles);\r\n                    if (imageFiles.length > 0) {\r\n                        toast.success(`Loaded ${imageFiles.length} existing image${imageFiles.length > 1 ? 's' : ''} for editing`);\r\n                    }\r\n                } catch (error) {\r\n                    console.error('Error loading existing images:', error);\r\n                    toast.error('Failed to load existing images');\r\n                }\r\n            };\r\n\r\n            loadExistingImages();\r\n        } else {\r\n            // Clear images if product has none\r\n            setSelectedFiles([]);\r\n            setExistingImageUrls([]);\r\n        }\r\n\r\n        // Scroll to form\r\n        formRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n    };\r\n\r\n    const handleNextImage = () => {\r\n        if (showImageModal && products.length > 0) {\r\n            const product = products.find(p => p.imageUrls.includes(showImageModal));\r\n            if (product) {\r\n                const nextIndex = (currentImageIndex + 1) % product.imageUrls.length;\r\n                setCurrentImageIndex(nextIndex);\r\n                setShowImageModal(product.imageUrls[nextIndex]);\r\n            }\r\n        }\r\n    };\r\n\r\n    const handlePrevImage = () => {\r\n        if (showImageModal && products.length > 0) {\r\n            const product = products.find(p => p.imageUrls.includes(showImageModal));\r\n            if (product) {\r\n                const prevIndex = (currentImageIndex - 1 + product.imageUrls.length) % product.imageUrls.length;\r\n                setCurrentImageIndex(prevIndex);\r\n                setShowImageModal(product.imageUrls[prevIndex]);\r\n            }\r\n        }\r\n    };\r\n\r\n    // Swipe handlers for image navigation\r\n    // Not used in the current implementation\r\n\r\n    const handleCategorySelect = (categoryId: string) => {\r\n        // Only allow selection from predefined CATEGORIES\r\n        const validCategory = CATEGORIES.find(cat => cat.id === categoryId);\r\n        if (validCategory) {\r\n            setSelectedCategory(prevCategory => prevCategory === categoryId ? null : categoryId);\r\n            const updatedFormData = getValues();\r\n            reset({ ...updatedFormData, category: categoryId }); // Use the category ID directly\r\n        } else {\r\n            console.error('Invalid category ID selected:', categoryId);\r\n            toast.error('Invalid category selected');\r\n        }\r\n    };\r\n\r\n    // Bulk delete all products\r\n    const bulkDeleteProducts = async () => {\r\n        try {\r\n            setIsBulkDeleting(true);\r\n            const totalProducts = products.length;\r\n            setBulkDeleteProgress({ current: 0, total: totalProducts });\r\n\r\n            // Delete products one by one\r\n            for (let i = 0; i < totalProducts; i++) {\r\n                const product = products[i];\r\n                setBulkDeleteProgress({ current: i + 1, total: totalProducts });\r\n\r\n                await databases.deleteDocument(\r\n                    appwriteConfig.databaseId,\r\n                    appwriteConfig.productsCollectionId,\r\n                    product.$id\r\n                );\r\n            }\r\n\r\n            toast.success('All products deleted successfully');\r\n            await fetchProducts(); // Refresh the products list\r\n        } catch (error) {\r\n            console.error('Error bulk deleting products:', error);\r\n            toast.error('Failed to delete all products');\r\n        } finally {\r\n            setIsBulkDeleting(false);\r\n            setShowBulkDeleteModal(false);\r\n        }\r\n    };\r\n\r\n    // Delete selected products\r\n    const deleteSelectedProducts = async () => {\r\n        try {\r\n            if (selectedProducts.length === 0) {\r\n                toast.error('No products selected');\r\n                return;\r\n            }\r\n\r\n            setIsBulkDeleting(true);\r\n            const totalSelected = selectedProducts.length;\r\n            setBulkDeleteProgress({ current: 0, total: totalSelected });\r\n\r\n            // Delete selected products one by one\r\n            for (let i = 0; i < totalSelected; i++) {\r\n                const productId = selectedProducts[i];\r\n                setBulkDeleteProgress({ current: i + 1, total: totalSelected });\r\n\r\n                await databases.deleteDocument(\r\n                    appwriteConfig.databaseId,\r\n                    appwriteConfig.productsCollectionId,\r\n                    productId\r\n                );\r\n            }\r\n\r\n            toast.success(`${totalSelected} product${totalSelected > 1 ? 's' : ''} deleted successfully`);\r\n            setSelectedProducts([]); // Clear selection\r\n            setIsMultiSelectMode(false); // Exit multi-select mode\r\n            await fetchProducts(); // Refresh the products list\r\n        } catch (error) {\r\n            console.error('Error deleting selected products:', error);\r\n            toast.error('Failed to delete selected products');\r\n        } finally {\r\n            setIsBulkDeleting(false);\r\n            setShowBulkDeleteModal(false);\r\n        }\r\n    };    // Toggle product selection\r\n    const toggleProductSelection = (productId: string) => {\r\n        setSelectedProducts(prev => {\r\n            if (prev.includes(productId)) {\r\n                return prev.filter(id => id !== productId);\r\n            } else {\r\n                return [...prev, productId];\r\n            }\r\n        });\r\n    };\r\n\r\n    // Set authorized to true for now - auth check removed temporarily\r\n    useEffect(() => {\r\n        setIsAuthorized(true);\r\n    }, []);\r\n\r\n    const imageVariants: Variants = {\r\n        initial: { opacity: 0, x: 100, position: \"relative\" },\r\n        animate: { opacity: 1, x: 0, position: \"relative\" },\r\n        exit: { opacity: 0, x: -100, position: \"absolute\" },\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50 pt-32 sm:pt-40 pb-8 sm:pb-12 px-4 sm:px-6 lg:px-8\">\r\n            {/* Loading screen for navigation - using LoadingScreen component for consistency */}\r\n            {isNavigating && <LoadingScreen message=\"Loading Receipt Generator...\" isFullScreen={true} />}\r\n            {isStockNavigating && <LoadingScreen message=\"Loading Stock Manager...\" isFullScreen={true} />}\r\n\r\n            <div className=\"max-w-7xl mx-auto\">\r\n                {/* Back button with animation */}\r\n                <motion.div\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    className=\"inline-block mb-6\"\r\n                >\r\n                    <Link\r\n                        href=\"/\"\r\n                        className=\"inline-flex items-center px-3 py-2 rounded-lg text-gray-700 hover:text-black hover:bg-gray-100 transition-all duration-200\"\r\n                    >\r\n                        <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            className=\"h-5 w-5 mr-2\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            stroke=\"currentColor\"\r\n                        >\r\n                            <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                strokeWidth={2}\r\n                                d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\r\n                            />\r\n                        </svg>\r\n                        Back to Home\r\n                    </Link>\r\n                </motion.div>\r\n\r\n                {/* Navigation buttons */}\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 sm:mb-12\">\r\n                    <h1 className=\"text-2xl sm:text-4xl font-bold text-gray-900 text-center sm:text-left tracking-tight\">\r\n                        Product Manager\r\n                    </h1>\r\n                    <div className=\"flex flex-col sm:flex-row justify-center sm:justify-end gap-3 mt-4 sm:mt-0\">\r\n                        {/* Receipt Generator Button */}\r\n                        <div className=\"relative w-full sm:w-auto\">\r\n                            <Link\r\n                                href=\"/admin/receipt-sender\"\r\n                                className=\"w-full text-center px-5 py-3 rounded-lg transition-all duration-200\r\n                                    flex items-center justify-center gap-2\r\n                                    bg-[#333333] hover:bg-gray-800 active:bg-gray-700\r\n                                    text-white font-medium\"\r\n                                style={{\r\n                                    WebkitTapHighlightColor: 'transparent',\r\n                                    touchAction: 'manipulation',\r\n                                    userSelect: 'none'\r\n                                }}\r\n                            >\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                                    </svg>\r\n                                    <span>Receipt Generator</span>\r\n                                </div>\r\n                            </Link>\r\n                        </div>\r\n\r\n                        {/* Stock Manager Button */}\r\n                        <div className=\"relative w-full sm:w-auto\">\r\n                            <Link\r\n                                href=\"/admin/stock-manager\"\r\n                                className=\"w-full text-center px-5 py-3 rounded-lg transition-all duration-200\r\n                                    flex items-center justify-center gap-2\r\n                                    bg-[#333333] hover:bg-gray-800 active:bg-gray-700\r\n                                    text-white font-medium\"\r\n                                style={{\r\n                                    WebkitTapHighlightColor: 'transparent',\r\n                                    touchAction: 'manipulation',\r\n                                    userSelect: 'none'\r\n                                }}\r\n                            >\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\r\n                                    </svg>\r\n                                    <span>Stock Manager</span>\r\n                                </div>\r\n                            </Link>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div>\r\n                    {/* Product Form */}\r\n                    <div ref={formRef} className=\"bg-white p-4 sm:p-8 rounded-xl sm:rounded-2xl shadow-lg mb-8 sm:mb-12 transition-all duration-300 hover:shadow-xl\">\r\n                        <h2 className=\"text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6\">\r\n                            {editingProduct ? 'Edit Product' : 'Add New Product'}\r\n                        </h2>\r\n                        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4 sm:space-y-6\">\r\n                            <div>\r\n                                <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Product Name</label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    {...register('name')}\r\n                                    className=\"w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg border border-gray-300 focus:ring-1 focus:ring-gray-400 focus:border-gray-400 transition-all duration-200 text-gray-900 font-normal placeholder-gray-500\"\r\n                                    placeholder=\"Enter product name\"\r\n                                />\r\n                                {errors.name && (\r\n                                    <p className=\"text-red-500 text-sm mt-2\">{errors.name.message}</p>\r\n                                )}\r\n                            </div>\r\n\r\n                            <div>\r\n                                <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Price</label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    {...register('price')}\r\n                                    className=\"w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg border border-gray-300 focus:ring-1 focus:ring-gray-400 focus:border-gray-400 transition-all duration-200 text-gray-900 font-normal placeholder-gray-500\"\r\n                                    placeholder=\"Enter price\"\r\n                                />\r\n                                {errors.price && (\r\n                                    <p className=\"text-red-500 text-sm mt-2\">{errors.price.message}</p>\r\n                                )}\r\n                            </div>\r\n\r\n                            <div>\r\n                                <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Description</label>\r\n                                <textarea\r\n                                    {...register('description')}\r\n                                    className=\"w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg border border-gray-300 focus:ring-1 focus:ring-gray-400 focus:border-gray-400 transition-all duration-200 text-gray-900 font-normal placeholder-gray-500 min-h-[120px]\"\r\n                                    placeholder=\"Enter product description\"\r\n                                />\r\n                                {errors.description && (\r\n                                    <p className=\"text-red-500 text-sm mt-2\">{errors.description.message}</p>\r\n                                )}\r\n                            </div>\r\n\r\n                            <div className=\"mt-4 sm:mt-8\">\r\n                                <h3 className=\"text-sm sm:text-base font-semibold text-gray-900 mb-2 sm:mb-4\">Category</h3>\r\n                                {categoriesLoading ? (\r\n                                    <div className=\"flex justify-center items-center py-6\">\r\n                                        <div className=\"animate-pulse flex space-x-4\">\r\n                                            <div className=\"flex-1 space-y-4 py-1\">\r\n                                                <div className=\"h-10 bg-gray-200 rounded w-full\"></div>\r\n                                                <div className=\"grid grid-cols-3 gap-4\">\r\n                                                    <div className=\"h-20 bg-gray-200 rounded\"></div>\r\n                                                    <div className=\"h-20 bg-gray-200 rounded\"></div>\r\n                                                    <div className=\"h-20 bg-gray-200 rounded\"></div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                ) : (\r\n                                    <div className=\"grid grid-cols-3 sm:grid-cols-6 gap-1.5 sm:gap-3\">\r\n                                        {CATEGORIES.map((category) => (\r\n                                            <motion.div\r\n                                                key={category.id}\r\n                                                whileHover={{ scale: 1.03 }}\r\n                                                whileTap={{ scale: 0.97 }}\r\n                                                className={`relative cursor-pointer p-2 sm:p-3 rounded-lg text-center transition-colors duration-200\r\n                                                    ${selectedCategory === category.id\r\n                                                        ? 'bg-black text-white'\r\n                                                        : 'bg-gray-100 hover:bg-gray-200 text-gray-900'\r\n                                                    }\r\n                                                `}\r\n                                                onClick={() => handleCategorySelect(category.id)}\r\n                                            >\r\n                                                <div className=\"text-lg sm:text-2xl mb-1\">{category.icon}</div>\r\n                                                <div className=\"text-[10px] sm:text-sm font-medium\">\r\n                                                    {category.name}\r\n                                                </div>\r\n                                            </motion.div>\r\n                                        ))}\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n\r\n                            <div>\r\n                                <label className=\"block text-sm font-semibold text-gray-700 mb-2\">Product Images</label>\r\n                                <div className=\"mt-1 flex flex-col space-y-4\">\r\n                                    <div\r\n                                        className={`flex justify-center px-4 sm:px-6 pt-4 pb-4 sm:pb-6 border-2 border-dashed rounded-lg transition-all duration-200 cursor-pointer ${isDragging\r\n                                            ? 'border-blue-500 bg-blue-50'\r\n                                            : selectedFiles.length > 0\r\n                                                ? 'border-green-500 hover:border-green-600'\r\n                                                : 'border-gray-300 hover:border-gray-400'\r\n                                            }`}\r\n                                        onDragEnter={(e) => {\r\n                                            e.preventDefault();\r\n                                            e.stopPropagation();\r\n                                            setIsDragging(true);\r\n                                        }}\r\n                                        onDragOver={(e) => {\r\n                                            e.preventDefault();\r\n                                            e.stopPropagation();\r\n                                            setIsDragging(true);\r\n                                        }}\r\n                                        onDragLeave={(e) => {\r\n                                            e.preventDefault();\r\n                                            e.stopPropagation();\r\n                                            setIsDragging(false);\r\n                                        }}\r\n                                        onDrop={(e) => {\r\n                                            e.preventDefault();\r\n                                            e.stopPropagation();\r\n                                            setIsDragging(false);\r\n\r\n                                            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\r\n                                                const droppedFiles = Array.from(e.dataTransfer.files);\r\n                                                const validatedFiles = validateFiles(droppedFiles);\r\n\r\n                                                if (validatedFiles.length > selectedFiles.length) {\r\n                                                    setSelectedFiles(validatedFiles);\r\n                                                    const newFilesCount = validatedFiles.length - selectedFiles.length;\r\n                                                    toast.success(`${newFilesCount} image${newFilesCount > 1 ? 's' : ''} added`);\r\n                                                }\r\n                                            }\r\n                                        }}\r\n                                        onClick={() => fileInputRef.current?.click()}\r\n                                    >\r\n                                        <div className=\"space-y-1 text-center\">\r\n                                            <svg className={`mx-auto h-10 w-10 sm:h-12 sm:w-12 ${isDragging ? 'text-blue-500' : 'text-gray-400'}`} stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\" aria-hidden=\"true\">\r\n                                                <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n                                            </svg>\r\n                                            <div className=\"flex flex-col items-center\">\r\n                                                <input\r\n                                                    type=\"file\"\r\n                                                    multiple\r\n                                                    ref={fileInputRef}\r\n                                                    onChange={(e) => {\r\n                                                        if (e.target.files && e.target.files.length > 0) {\r\n                                                            const selectedInputFiles = Array.from(e.target.files);\r\n                                                            const validatedFiles = validateFiles(selectedInputFiles);\r\n\r\n                                                            if (validatedFiles.length > selectedFiles.length) {\r\n                                                                setSelectedFiles(validatedFiles);\r\n                                                                const newFilesCount = validatedFiles.length - selectedFiles.length;\r\n                                                                toast.success(`${newFilesCount} image${newFilesCount > 1 ? 's' : ''} selected`);\r\n                                                            }\r\n                                                        }\r\n                                                    }}\r\n                                                    className=\"hidden\"\r\n                                                    accept=\"image/*\"\r\n                                                />\r\n                                                <button\r\n                                                    type=\"button\"\r\n                                                    className=\"mt-2 text-sm font-medium text-blue-600 hover:text-blue-700 focus:outline-none\"\r\n                                                    onClick={(e) => {\r\n                                                        e.stopPropagation();\r\n                                                        fileInputRef.current?.click();\r\n                                                    }}\r\n                                                >\r\n                                                    Click to upload\r\n                                                </button>\r\n                                                <p className=\"text-xs text-gray-500 mt-1\">or drag and drop</p>\r\n                                            </div>\r\n                                            <p className=\"text-xs text-gray-500 mt-2\">PNG, JPG, GIF up to 10MB (Max 3 images)</p>\r\n                                            {selectedFiles.length > 0 && (\r\n                                                <p className=\"text-xs font-medium text-green-600 mt-2\">\r\n                                                    {selectedFiles.length} image{selectedFiles.length > 1 ? 's' : ''} selected\r\n                                                </p>\r\n                                            )}\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Selected Files Preview */}\r\n                                    {selectedFiles.length > 0 && (\r\n                                        <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-4\">\r\n                                            {selectedFiles.map((file, index) => (\r\n                                                <div key={index} className=\"relative group overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300\">\r\n                                                    <div className=\"relative h-24 sm:h-32 bg-gray-100\">\r\n                                                        <Image\r\n                                                            src={URL.createObjectURL(file)}\r\n                                                            alt={`Selected ${index + 1}`}\r\n                                                            width={100}\r\n                                                            height={100}\r\n                                                            className=\"w-full h-full object-cover\"\r\n                                                        />\r\n                                                        <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300\"></div>\r\n                                                    </div>\r\n                                                    <div className=\"absolute top-2 right-2 flex gap-2\">\r\n                                                        <button\r\n                                                            type=\"button\"\r\n                                                            onClick={() => {\r\n                                                                // Remove the file from selectedFiles\r\n                                                                setSelectedFiles(selectedFiles.filter((_, i) => i !== index));\r\n                                                                // Also remove the corresponding URL from existingImageUrls if it exists\r\n                                                                if (existingImageUrls[index]) {\r\n                                                                    setExistingImageUrls(existingImageUrls.filter((_, i) => i !== index));\r\n                                                                }\r\n                                                                toast.success('Image removed');\r\n                                                            }}\r\n                                                            className=\"bg-red-500 text-white rounded-full p-1.5 shadow-md transform scale-90 opacity-0 group-hover:opacity-100 group-hover:scale-100 transition-all duration-300\"\r\n                                                            title=\"Remove image\"\r\n                                                        >\r\n                                                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                                                            </svg>\r\n                                                        </button>\r\n                                                    </div>\r\n                                                    <div className=\"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs py-1 px-2 truncate\">\r\n                                                        {file.name}\r\n                                                    </div>\r\n                                                </div>\r\n                                            ))}\r\n                                            {selectedFiles.length < 3 && (\r\n                                                <div\r\n                                                    className=\"relative h-24 sm:h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-gray-400 transition-all duration-200\"\r\n                                                    onClick={() => fileInputRef.current?.click()}\r\n                                                >\r\n                                                    <div className=\"text-center\">\r\n                                                        <svg className=\"mx-auto h-8 w-8 text-gray-400\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n                                                        </svg>\r\n                                                        <p className=\"text-xs text-gray-500 mt-1\">Add more</p>\r\n                                                    </div>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    disabled={isLoading}\r\n                                    className=\"w-full sm:flex-1 bg-[#333333] text-white px-4 sm:px-6 py-3 rounded-lg font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\r\n                                >\r\n                                    {isLoading ? 'Saving...' : editingProduct ? 'Update Product' : 'Add Product'}\r\n                                </button>\r\n\r\n                                {editingProduct ? (\r\n                                    <button\r\n                                        type=\"button\"\r\n                                        onClick={cancelEdit}\r\n                                        className=\"w-full sm:flex-1 bg-gray-100 text-gray-700 px-4 sm:px-6 py-3 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200\"\r\n                                    >\r\n                                        Cancel Edit\r\n                                    </button>\r\n                                ) : (\r\n                                    <button\r\n                                        type=\"button\"\r\n                                        onClick={clearForm}\r\n                                        className=\"w-full sm:flex-1 bg-gray-100 text-gray-700 px-4 sm:px-6 py-3 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200\"\r\n                                    >\r\n                                        Clear Form\r\n                                    </button>\r\n                                )}\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n\r\n                    {/* Products List */}\r\n                    <div className=\"bg-white p-4 sm:p-8 rounded-xl sm:rounded-2xl shadow-lg\">\r\n                        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0 mb-6 sm:mb-8\">\r\n                            <div>\r\n                                <h2 className=\"text-xl sm:text-2xl font-bold text-gray-800 mb-2\">Products List</h2>\r\n                                <p className=\"text-gray-600\">\r\n                                    Total Products: <span className=\"font-semibold text-[#333333]\">{products.length}</span>\r\n                                    {isMultiSelectMode && (\r\n                                        <span className=\"ml-2 text-blue-600\">\r\n                                            Selected: <span className=\"font-semibold\">{selectedProducts.length}</span>\r\n                                        </span>\r\n                                    )}\r\n                                </p>\r\n                            </div>\r\n                            <div className=\"flex flex-col sm:flex-row items-end sm:items-center gap-2 sm:gap-4\">\r\n                                {/* Multi-select mode toggle */}\r\n                                {products.length > 0 && (\r\n                                    <button\r\n                                        onClick={() => {\r\n                                            setIsMultiSelectMode(!isMultiSelectMode);\r\n                                            if (isMultiSelectMode) {\r\n                                                setSelectedProducts([]);\r\n                                            }\r\n                                        }}\r\n                                        className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-1\r\n                                            ${isMultiSelectMode\r\n                                                ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'\r\n                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n                                            }`}\r\n                                    >\r\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n                                        </svg>\r\n                                        {isMultiSelectMode ? 'Exit Selection' : 'Select Multiple'}\r\n                                    </button>\r\n                                )}\r\n\r\n                                {/* Delete Selected Button */}\r\n                                {isMultiSelectMode && selectedProducts.length > 0 && (\r\n                                    <button\r\n                                        onClick={() => setShowBulkDeleteModal(true)}\r\n                                        className=\"px-3 py-2 bg-red-50 text-red-600 rounded-lg text-sm font-medium hover:bg-red-100 transition-all duration-200 flex items-center gap-1\"\r\n                                    >\r\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\r\n                                        </svg>\r\n                                        Delete Selected ({selectedProducts.length})\r\n                                    </button>\r\n                                )}\r\n\r\n                                {/* Delete All Products Button */}\r\n                                {products.length > 0 && !isMultiSelectMode && (\r\n                                    <button\r\n                                        onClick={() => {\r\n                                            setSelectedProducts([]); // Clear any previous selection\r\n                                            setShowBulkDeleteModal(true);\r\n                                        }}\r\n                                        className=\"px-3 py-2 bg-red-50 text-red-600 rounded-lg text-sm font-medium hover:bg-red-100 transition-all duration-200 flex items-center gap-1\"\r\n                                    >\r\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\r\n                                        </svg>\r\n                                        Delete All\r\n                                    </button>\r\n                                )}\r\n\r\n                                <div className=\"flex items-center gap-2 sm:gap-4\">\r\n                                    <label className=\"text-sm text-gray-600 whitespace-nowrap\">Sort by:</label>\r\n                                    <select\r\n                                        className=\"w-full sm:w-auto px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white focus:ring-1 focus:ring-gray-400 focus:border-gray-400 transition-all duration-200\"\r\n                                        onChange={(e) => {\r\n                                            const order = e.target.value === 'newest' ? 'desc' : 'asc';\r\n                                            setSortOrder(order);\r\n                                            fetchProducts();\r\n                                        }}\r\n                                    >\r\n                                        <option value=\"newest\">Newest First</option>\r\n                                        <option value=\"oldest\">Oldest First</option>\r\n                                    </select>\r\n                                </div>\r\n\r\n                                {/* Bulk Upload Products Button */}\r\n                                <Link\r\n                                    href=\"/admin/product-uploader\"\r\n                                    className=\"px-3 py-2 bg-gradient-to-r from-blue-600 to-pink-500 text-white rounded-lg text-sm font-medium hover:shadow-md transition-all duration-200 flex items-center gap-1\"\r\n                                >\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\r\n                                    </svg>\r\n                                    Bulk Upload\r\n                                </Link>\r\n\r\n                                {/* Update Products Button */}\r\n                                <Link\r\n                                    href=\"/admin/update-products\"\r\n                                    className=\"px-3 py-2 bg-gradient-to-r from-green-600 to-teal-500 text-white rounded-lg text-sm font-medium hover:shadow-md transition-all duration-200 flex items-center gap-1\"\r\n                                >\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                                    </svg>\r\n                                    Update Categories\r\n                                </Link>\r\n                            </div>\r\n                        </div>\r\n                        {/* Products grid with scrollbar - similar to stock manager */}\r\n                        <div className=\"max-h-[800px] overflow-y-auto pr-2 pb-4 custom-scrollbar\">\r\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8\">\r\n                                {products.map((product) => (\r\n                                    <div\r\n                                        key={product.$id}\r\n                                        className={`bg-white rounded-lg sm:rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 border ${isMultiSelectMode && selectedProducts.includes(product.$id)\r\n                                            ? 'border-blue-500 ring-2 ring-blue-200'\r\n                                            : 'border-gray-100'\r\n                                            }`}\r\n                                        onClick={() => {\r\n                                            if (isMultiSelectMode) {\r\n                                                toggleProductSelection(product.$id);\r\n                                            }\r\n                                        }}\r\n                                    >\r\n                                        {product.imageUrls && product.imageUrls.length > 0 && (\r\n                                            <div className=\"relative h-48 sm:h-64 overflow-hidden\">\r\n                                                {product.imageUrls.length > 0 && (\r\n                                                    <Image\r\n                                                        src={product.imageUrls[0]}\r\n                                                        alt={`${product.name} 1`}\r\n                                                        width={100}\r\n                                                        height={100}\r\n                                                        className=\"w-full h-full object-cover cursor-pointer\"\r\n                                                        onClick={() => {\r\n                                                            setCurrentImageIndex(0);\r\n                                                            setShowImageModal(product.imageUrls[0]);\r\n                                                        }}\r\n                                                    />\r\n                                                )}\r\n                                                {product.imageUrls.length > 1 && (\r\n                                                    <div className=\"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded-full text-xs\">\r\n                                                        {product.imageUrls.length} images\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n                                        )}                                    <div className=\"p-4 sm:p-6\">\r\n                                            <div className=\"flex justify-between items-start\">\r\n                                                <div>\r\n                                                    <h3 className=\"text-lg sm:text-xl font-bold text-gray-900 mb-2\">{product.name}</h3>\r\n                                                    <p className=\"text-sm font-medium text-gray-700\">\r\n                                                        {CATEGORIES.find(cat => cat.id === product.category)?.name || 'No category'}\r\n                                                    </p>\r\n                                                </div>\r\n                                                {isMultiSelectMode && (\r\n                                                    <div\r\n                                                        className={`w-5 h-5 rounded border flex items-center justify-center ${selectedProducts.includes(product.$id)\r\n                                                            ? 'bg-blue-500 border-blue-500'\r\n                                                            : 'border-gray-300'\r\n                                                            }`}\r\n                                                        onClick={(e) => {\r\n                                                            e.stopPropagation();\r\n                                                            toggleProductSelection(product.$id);\r\n                                                        }}\r\n                                                    >\r\n                                                        {selectedProducts.includes(product.$id) && (\r\n                                                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                                                            </svg>\r\n                                                        )}\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n                                            <p className=\"text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3\">₦{product.price}</p>\r\n                                            <p className=\"text-gray-600 mb-4 sm:mb-6 line-clamp-2\">{product.description}</p>\r\n                                            <div className=\"flex gap-2 sm:gap-3\">\r\n                                                <button\r\n                                                    onClick={() => handleEdit(product)}\r\n                                                    className=\"flex-1 bg-[#333333] text-white px-3 sm:px-4 py-2 rounded-lg font-medium hover:bg-gray-800 transition-all duration-200\"\r\n                                                >\r\n                                                    Edit\r\n                                                </button>\r\n                                                <button\r\n                                                    onClick={() => setShowDeleteModal(product.$id)}\r\n                                                    className=\"flex-1 bg-red-50 text-red-600 px-3 sm:px-4 py-2 rounded-lg font-medium hover:bg-red-100 transition-all duration-200\"\r\n                                                >\r\n                                                    Delete\r\n                                                </button>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Modals */}\r\n                    <AnimatePresence>\r\n                        {showImageModal && (\r\n                            <motion.div className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\r\n                                <div className=\"bg-white p-4 sm:p-6 rounded-lg shadow-xl w-full max-w-[90%] sm:max-w-[80%] lg:max-w-[60%] max-h-[calc(100vh-60px)] overflow-auto relative\">\r\n                                    <AnimatePresence initial={false} custom={currentImageIndex}>\r\n                                        <motion.div\r\n                                            key={showImageModal}\r\n                                            variants={imageVariants}\r\n                                            initial=\"initial\"\r\n                                            animate=\"animate\"\r\n                                            exit=\"exit\"\r\n                                            transition={{ duration: 0.5, ease: \"easeInOut\" }}\r\n                                            className=\"w-full h-auto object-contain\"\r\n                                        >                                    {showImageModal && (\r\n                                            <Image\r\n                                                src={showImageModal}\r\n                                                alt=\"Product\"\r\n                                                width={100}\r\n                                                height={100}\r\n                                                className=\"w-full h-auto object-contain\"\r\n                                            />\r\n                                        )}\r\n                                        </motion.div>\r\n                                    </AnimatePresence>\r\n                                    <button\r\n                                        className=\"absolute top-1/2 left-4 transform -translate-y-1/2 bg-gray-800 text-white p-2 rounded-full hover:bg-gray-700 transition-all duration-200\"\r\n                                        onClick={handlePrevImage}\r\n                                    >\r\n                                        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\r\n                                        </svg>\r\n                                    </button>\r\n                                    <button\r\n                                        className=\"absolute top-1/2 right-4 transform -translate-y-1/2 bg-gray-800 text-white p-2 rounded-full hover:bg-gray-700 transition-all duration-200\"\r\n                                        onClick={handleNextImage}\r\n                                    >\r\n                                        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\r\n                                        </svg>\r\n                                    </button>\r\n                                </div>\r\n                            </motion.div>\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                    <AnimatePresence>\r\n                        {showDeleteModal && (\r\n                            <motion.div className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\r\n                                <motion.div\r\n                                    className=\"bg-white p-4 sm:p-6 rounded-lg shadow-xl w-full max-w-sm\"\r\n                                    initial={{ scale: 0.8 }}\r\n                                    animate={{ scale: 1 }}\r\n                                    exit={{ scale: 0.8 }}\r\n                                    onClick={(e) => e.stopPropagation()}\r\n                                >\r\n                                    <h2 className=\"text-lg font-bold text-gray-900 mb-4\">Confirm Delete</h2>\r\n                                    <p className=\"text-gray-600 mb-6\">Are you sure you want to delete this product?</p>\r\n                                    <div className=\"flex justify-end gap-4\">\r\n                                        <button\r\n                                            className=\"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200\"\r\n                                            onClick={() => setShowDeleteModal(null)}\r\n                                        >\r\n                                            Cancel\r\n                                        </button>\r\n                                        <button\r\n                                            className=\"bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-all duration-200\"\r\n                                            onClick={confirmDelete}\r\n                                        >\r\n                                            Delete\r\n                                        </button>\r\n                                    </div>\r\n                                </motion.div>\r\n                            </motion.div>\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                    {/* Category Delete Confirmation Modal */}\r\n                    <AnimatePresence>\r\n                        {showCategoryDeleteModal && (\r\n                            <motion.div className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\r\n                                <motion.div\r\n                                    className=\"bg-white p-4 sm:p-6 rounded-lg shadow-xl w-full max-w-sm\"\r\n                                    initial={{ scale: 0.8 }}\r\n                                    animate={{ scale: 1 }}\r\n                                    exit={{ scale: 0.8 }}\r\n                                    onClick={(e) => e.stopPropagation()}\r\n                                >\r\n                                    <h2 className=\"text-lg font-bold text-gray-900 mb-4\">Confirm Delete Category</h2>\r\n                                    <p className=\"text-gray-600 mb-2\">Are you sure you want to delete this category?</p>\r\n                                    <p className=\"text-red-600 text-sm mb-6\">This action cannot be undone and may affect products using this category.</p>\r\n                                    <div className=\"flex justify-end gap-4\">\r\n                                        <button\r\n                                            className=\"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200\"\r\n                                            onClick={() => setShowCategoryDeleteModal(null)}\r\n                                        >\r\n                                            Cancel\r\n                                        </button>\r\n                                        <button\r\n                                            className=\"bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-all duration-200\"\r\n                                            onClick={confirmCategoryDelete}\r\n                                        >\r\n                                            Delete\r\n                                        </button>\r\n                                    </div>\r\n                                </motion.div>\r\n                            </motion.div>\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                    {/* Bulk Delete Products Confirmation Modal */}\r\n                    <AnimatePresence>\r\n                        {showBulkDeleteModal && (\r\n                            <motion.div\r\n                                className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\r\n                                initial={{ opacity: 0 }}\r\n                                animate={{ opacity: 1 }}\r\n                                exit={{ opacity: 0 }}\r\n                                onClick={() => !isBulkDeleting && setShowBulkDeleteModal(false)}\r\n                            >\r\n                                <motion.div\r\n                                    className=\"bg-white p-4 sm:p-6 rounded-lg shadow-xl w-full max-w-md\"\r\n                                    initial={{ scale: 0.8 }}\r\n                                    animate={{ scale: 1 }}\r\n                                    exit={{ scale: 0.8 }}\r\n                                    onClick={(e) => e.stopPropagation()}\r\n                                >\r\n                                    {isBulkDeleting ? (\r\n                                        <div className=\"flex flex-col items-center py-4\">\r\n                                            <SpinningLoader size=\"large\" />\r\n                                            <p className=\"mt-4 text-gray-800 font-medium\">\r\n                                                Deleting products... ({bulkDeleteProgress.current} of {bulkDeleteProgress.total})\r\n                                            </p>\r\n                                            <div className=\"w-full mt-4 bg-gray-200 rounded-full h-2.5\">\r\n                                                <div\r\n                                                    className=\"bg-gradient-to-r from-blue-500 to-pink-500 h-2.5 rounded-full\"\r\n                                                    style={{ width: `${(bulkDeleteProgress.current / bulkDeleteProgress.total) * 100}%` }}\r\n                                                ></div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <>\r\n                                            <h2 className=\"text-lg font-bold text-gray-900 mb-4\">\r\n                                                {selectedProducts.length > 0 ? 'Delete Selected Products' : 'Delete All Products'}\r\n                                            </h2>\r\n                                            <p className=\"text-gray-600 mb-2\">\r\n                                                {selectedProducts.length > 0\r\n                                                    ? 'Are you sure you want to delete the selected products?'\r\n                                                    : 'Are you sure you want to delete all products?'\r\n                                                }\r\n                                            </p>\r\n                                            <p className=\"text-red-600 text-sm mb-6\">\r\n                                                This will delete <span className=\"font-bold\">\r\n                                                    {selectedProducts.length > 0 ? selectedProducts.length : products.length}\r\n                                                    product{(selectedProducts.length > 0 ? selectedProducts.length : products.length) !== 1 ? 's' : ''}\r\n                                                </span> and cannot be undone.\r\n                                            </p>\r\n                                            <div className=\"flex justify-end gap-4\">\r\n                                                <button\r\n                                                    className=\"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200\"\r\n                                                    onClick={() => setShowBulkDeleteModal(false)}\r\n                                                >\r\n                                                    Cancel\r\n                                                </button>\r\n                                                <button\r\n                                                    className=\"bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-all duration-200\"\r\n                                                    onClick={selectedProducts.length > 0 ? deleteSelectedProducts : bulkDeleteProducts}\r\n                                                >\r\n                                                    {selectedProducts.length > 0 ? 'Delete Selected' : 'Delete All'}\r\n                                                </button>\r\n                                            </div>\r\n                                        </>\r\n                                    )}\r\n                                </motion.div>\r\n                            </motion.div>\r\n                        )}\r\n                    </AnimatePresence>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AdminPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AAGA;AACA;AACA;AACA;AACA;AARA;AAFA;AAIA,+CAA+C;AAC/C;AAAA;;;AAVA;;;;;;;;;;;;;;AAgBA,mDAAmD;AAGnD,+CAA+C;AAC/C,yCAAyC;AACzC,MAAM,mBAAmB,4HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;AAErD,MAAM,gBAAgB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAA,MAAO,iBAAiB,QAAQ,CAAC,MAAM;QAC/D,SAAS;IACb,GAAG,QAAQ;AACf;AA0BA,MAAM,YAAY;;IACd,sDAAsD;IAEtD,mBAAmB;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAe,uBAAuB;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE,GAAW,mBAAmB;IACpF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE,GAAI,uBAAuB;IACxF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE,GAAG,4BAA4B;IACtG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAqB,gBAAgB;IAChF,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAe,2BAA2B;IAC1E,MAAM,CAAC,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAe,2BAA2B;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,2BAA2B;IAC7F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,OAAO,4BAA4B;IACxG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAQ,yBAAyB;IACrG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,SAAgB,0BAA0B;IACrG,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAgB,+BAA+B;IAC1G,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,4BAA4B;IACzG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,+CAA+C;IAC9H,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,wCAAwC;IAC7F,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB,OAAO,kCAAkC;IACvF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,iCAAiC;IACxG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,4BAA4B;IACzF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,SAAS;QAAG,OAAO;IAAE,IAAI,uBAAuB;IAC/G,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE,GAAG,qCAAqC;IAC7G,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,2BAA2B;IAC9F,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,qCAAqC;IAElI,yBAAyB;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,uDAAuD;IACvD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QACjG,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IAC1B;IAEA,kEAAkE;IAClE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC9B,IAAI;gBACA,oDAAoD;gBACpD,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAC1C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;oBACI,iJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,iJAAA,CAAA,QAAK,CAAC,MAAM,CAAC;oBACb,cAAc,QAAQ,iJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,gBAAgB,iJAAA,CAAA,QAAK,CAAC,SAAS,CAAC;iBACxE;gBAGL,qDAAqD;gBACrD,IAAI,eAAe;uBAAI,SAAS,SAAS;iBAAC;gBAE1C,yEAAyE;gBACzE,IAAI,SAAS,KAAK,GAAG,KAAK;oBACtB,2CAA2C;oBAC3C,MAAM,gBAAgB,KAAK,IAAI,CAAC,SAAS,KAAK,GAAG;oBAEjD,gDAAgD;oBAChD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACpC,MAAM,SAAS,IAAI;wBACnB,MAAM,qBAAqB,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CACpD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;4BACI,iJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4BACZ,iJAAA,CAAA,QAAK,CAAC,MAAM,CAAC;4BACb,cAAc,QAAQ,iJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,gBAAgB,iJAAA,CAAA,QAAK,CAAC,SAAS,CAAC;yBACxE;wBAGL,mCAAmC;wBACnC,eAAe;+BAAI;+BAAiB,mBAAmB,SAAS;yBAAC;oBACrE;gBACJ;gBAEA,YAAY;YAChB,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB;QACJ;+CAAG;QAAC;KAAU;IAEd,sDAAsD;IACtD,MAAM,YAAY;QACd,kBAAkB;QAClB,oBAAoB;QACpB,iBAAiB,EAAE;QACnB,qBAAqB,EAAE,GAAG,4BAA4B;QACtD,MAAM;YACF,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACd;QACA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAClB;IAEA,uBAAuB;IACvB,MAAM,aAAa;QACf;QACA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAClB;IAEA,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,MAAM;6DAAwB;oBAC1B,IAAI;wBACA,aAAa;wBACb,oDAAoD;wBACpD,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAC1C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;4BACI,iJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4BACZ,iJAAA,CAAA,QAAK,CAAC,MAAM,CAAC;4BACb,cAAc,QAAQ,iJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,gBAAgB,iJAAA,CAAA,QAAK,CAAC,SAAS,CAAC;yBACxE;wBAGL,qDAAqD;wBACrD,IAAI,eAAe;+BAAI,SAAS,SAAS;yBAAC;wBAE1C,yEAAyE;wBACzE,IAAI,SAAS,KAAK,GAAG,KAAK;4BACtB,2CAA2C;4BAC3C,MAAM,gBAAgB,KAAK,IAAI,CAAC,SAAS,KAAK,GAAG;4BAEjD,gDAAgD;4BAChD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gCACpC,MAAM,SAAS,IAAI;gCACnB,MAAM,qBAAqB,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CACpD,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;oCACI,iJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oCACZ,iJAAA,CAAA,QAAK,CAAC,MAAM,CAAC;oCACb,cAAc,QAAQ,iJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,gBAAgB,iJAAA,CAAA,QAAK,CAAC,SAAS,CAAC;iCACxE;gCAGL,mCAAmC;gCACnC,eAAe;uCAAI;uCAAiB,mBAAmB,SAAS;iCAAC;4BACrE;wBACJ;wBAEA,YAAY;oBAChB,EAAE,OAAO,OAAO;wBACZ,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACN,aAAa;oBACjB;gBACJ;;YAEA,IAAI,cAAc;gBACd;YACJ;QACJ;8BAAG;QAAC;QAAc;KAAU,GAAM,2EAA2E;IAC7G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,qBAAqB;QACzB;8BAAG,EAAE;IAEL,wDAAwD;IACxD,MAAM,WAAW,OAAO;QACpB,IAAI;YACA,aAAa;YACb,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,gDAAgD;YAChD,IAAI,YAAsB,EAAE;YAE5B,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC1B,IAAI;oBACA,QAAQ,GAAG,CAAC;oBAEZ,2CAA2C;oBAC3C,MAAM,WAAmB,EAAE;oBAC3B,MAAM,eAAyB,EAAE;oBAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;wBAC3C,MAAM,OAAO,aAAa,CAAC,EAAE;wBAC7B,MAAM,2BAA2B,iBAAiB,CAAC,EAAE;wBAErD,4FAA4F;wBAC5F,IAAI,4BAA4B,KAAK,IAAI,CAAC,UAAU,CAAC,oBAAoB;4BACrE,mDAAmD;4BACnD,aAAa,IAAI,CAAC;4BAClB,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;wBACzD,OAAO;4BACH,4CAA4C;4BAC5C,SAAS,IAAI,CAAC;4BACd,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;wBACtD;oBACJ;oBAEA,wBAAwB;oBACxB,IAAI,SAAS,MAAM,GAAG,GAAG;wBACrB,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,cAAc,CAAC;wBAC5D,MAAM,iBAAiB,SAAS,GAAG,CAAC,CAAA,OAChC,yHAAA,CAAA,UAAO,CAAC,UAAU,CACd,yHAAA,CAAA,iBAAc,CAAC,SAAS,EACxB,iJAAA,CAAA,KAAE,CAAC,MAAM,IACT;wBAIR,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CAAC;wBACxC,oCAAoC;wBACpC,MAAM,eAAe,cAAc,GAAG,CAAC,CAAA;4BACnC,IAAI,QAAQ,KAAK,GAAG,EAAE;gCAClB,kEAAkE;gCAClE,OAAO,yHAAA,CAAA,UAAO,CAAC,WAAW,CACtB,yHAAA,CAAA,iBAAc,CAAC,SAAS,EACxB,KAAK,GAAG,EACV,QAAQ;4BACd,OAAO;gCACH,QAAQ,KAAK,CAAC;gCACd,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gCACZ,aAAa;gCACb,OAAO;4BACX;wBACJ;wBAEA,2DAA2D;wBAC3D,YAAY,EAAE;wBACd,IAAI,cAAc;wBAClB,IAAI,mBAAmB;wBAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;4BAC3C,MAAM,OAAO,aAAa,CAAC,EAAE;4BAC7B,MAAM,2BAA2B,iBAAiB,CAAC,EAAE;4BAErD,IAAI,4BAA4B,KAAK,IAAI,CAAC,UAAU,CAAC,oBAAoB;gCACrE,mBAAmB;gCACnB,UAAU,IAAI,CAAC,YAAY,CAAC,iBAAiB;gCAC7C;4BACJ,OAAO;gCACH,cAAc;gCACd,UAAU,IAAI,CAAC,YAAY,CAAC,YAAY;gCACxC;4BACJ;wBACJ;wBAEA,QAAQ,GAAG,CAAC;oBAChB,OAAO;wBACH,sDAAsD;wBACtD,YAAY;wBACZ,QAAQ,GAAG,CAAC;oBAChB;gBACJ,EAAE,OAAO,WAAoB;oBACzB,MAAM,gBAAgB;oBACtB,QAAQ,KAAK,CAAC,wBAAwB;oBACtC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ,aAAa;oBACb;gBACJ;YACJ,EAAa,oFAAoF;YACjG,MAAM,WAAW,4HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACnD,IAAI,CAAC,UAAU;gBACX,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,aAAa;gBACb;YACJ;YACA,MAAM,cAAc;gBAChB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,UAAU,oBAAoB,gBAAgB,YAAY;gBAC1D,WAAW,UAAU,MAAM,GAAG,IAAI,YAAa,gBAAgB,aAAa,EAAE;YAClF;YAEA,QAAQ,GAAG,CAAC,iDAAiD;YAE7D,IAAI,gBAAgB;gBAChB,IAAI;oBACA,QAAQ,GAAG,CAAC;oBACZ,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC1B,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC,eAAe,GAAG,EAClB;oBAEJ,QAAQ,GAAG,CAAC;oBACZ,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAClB,EAAE,OAAO,aAAsB;oBAC3B,MAAM,gBAAgB;oBACtB,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ,aAAa;oBACb;gBACJ;YACJ,OAAO;gBACH,IAAI;oBACA,QAAQ,GAAG,CAAC;oBACZ,MAAM,aAAa,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC7C,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC,iJAAA,CAAA,KAAE,CAAC,MAAM,IACT;oBAEJ,QAAQ,GAAG,CAAC,mCAAmC;oBAC/C,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;oBACd,aAAa,6CAA6C;gBAC9D,EAAE,OAAO,aAAsB;oBAC3B,MAAM,gBAAgB;oBACtB,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ,aAAa;oBACb;gBACJ;YACJ;YAEA,mDAAmD;YACnD;YACA,MAAM;QACV,EAAE,OAAO,OAAgB;YACrB,MAAM,gBAAgB;YACtB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,aAAa;QACjB;IACJ;IAEA,2CAA2C;IAC3C,MAAM,gBAAgB,CAAC;QACnB,8BAA8B;QAC9B,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;QAE7D,uCAAuC;QACvC,IAAI,WAAW,MAAM,GAAG,MAAM,MAAM,EAAE;YAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;QAEA,8BAA8B;QAC9B,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA;YACrC,MAAM,cAAc,KAAK,IAAI,IAAI,KAAK,OAAO,MAAM,OAAO;YAC1D,IAAI,CAAC,aAAa;gBACd,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,oBAAoB,CAAC;YACxD;YACA,OAAO;QACX;QAEA,mFAAmF;QACnF,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,sBAAsB,kBAAkB,MAAM,GAAG;QACzH,MAAM,aAAa;eAAI;eAAiB;SAAe,CAAC,KAAK,CAAC,GAAG;QAEjE,uCAAuC;QACvC,IAAI,aAAa,MAAM,GAAG,eAAe,MAAM,GAAG,GAAG;YACjD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;QAEA,OAAO;IACX;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB;QAClB,IAAI,iBAAiB;YACjB,IAAI;gBACA,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC1B,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;gBAEJ,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,mBAAmB;gBACnB,MAAM;YACV,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB;QACJ;IACJ;IAEA,MAAM,wBAAwB;QAC1B,IAAI,CAAC,yBAAyB;QAE9B,IAAI;YACA,wEAAwE;YACxE,2BAA2B;YAC3B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,kBAAkB;QAElB,+BAA+B;QAC/B,MAAM;YACF,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,WAAW;YAChC,UAAU,QAAQ,QAAQ;QAC9B;QAEA,oCAAoC;QACpC,oBAAoB,QAAQ,QAAQ,IAAI;QAExC,mDAAmD;QACnD,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;YACnD,gCAAgC;YAChC,qBAAqB,QAAQ,SAAS;YAEtC,MAAM,qBAAqB;gBACvB,IAAI;oBACA,MAAM,aAAqB,EAAE;oBAE7B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,SAAS,CAAC,MAAM,EAAE,IAAK;wBAC/C,MAAM,WAAW,QAAQ,SAAS,CAAC,EAAE;wBACrC,IAAI;4BACA,6CAA6C;4BAC7C,MAAM,WAAW,MAAM,MAAM;4BAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,MAAM,WAAW,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO;4BAC9E,MAAM,OAAO,IAAI,KAAK;gCAAC;6BAAK,EAAE,UAAU;gCAAE,MAAM,KAAK,IAAI;4BAAC;4BAC1D,WAAW,IAAI,CAAC;wBACpB,EAAE,OAAO,YAAY;4BACjB,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;wBACnD;oBACJ;oBAEA,iBAAiB;oBACjB,IAAI,WAAW,MAAM,GAAG,GAAG;wBACvB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,WAAW,MAAM,CAAC,eAAe,EAAE,WAAW,MAAM,GAAG,IAAI,MAAM,GAAG,YAAY,CAAC;oBAC7G;gBACJ,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBAChB;YACJ;YAEA;QACJ,OAAO;YACH,mCAAmC;YACnC,iBAAiB,EAAE;YACnB,qBAAqB,EAAE;QAC3B;QAEA,iBAAiB;QACjB,QAAQ,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IACzD;IAEA,MAAM,kBAAkB;QACpB,IAAI,kBAAkB,SAAS,MAAM,GAAG,GAAG;YACvC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,QAAQ,CAAC;YACxD,IAAI,SAAS;gBACT,MAAM,YAAY,CAAC,oBAAoB,CAAC,IAAI,QAAQ,SAAS,CAAC,MAAM;gBACpE,qBAAqB;gBACrB,kBAAkB,QAAQ,SAAS,CAAC,UAAU;YAClD;QACJ;IACJ;IAEA,MAAM,kBAAkB;QACpB,IAAI,kBAAkB,SAAS,MAAM,GAAG,GAAG;YACvC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,QAAQ,CAAC;YACxD,IAAI,SAAS;gBACT,MAAM,YAAY,CAAC,oBAAoB,IAAI,QAAQ,SAAS,CAAC,MAAM,IAAI,QAAQ,SAAS,CAAC,MAAM;gBAC/F,qBAAqB;gBACrB,kBAAkB,QAAQ,SAAS,CAAC,UAAU;YAClD;QACJ;IACJ;IAEA,sCAAsC;IACtC,yCAAyC;IAEzC,MAAM,uBAAuB,CAAC;QAC1B,kDAAkD;QAClD,MAAM,gBAAgB,4HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACxD,IAAI,eAAe;YACf,oBAAoB,CAAA,eAAgB,iBAAiB,aAAa,OAAO;YACzE,MAAM,kBAAkB;YACxB,MAAM;gBAAE,GAAG,eAAe;gBAAE,UAAU;YAAW,IAAI,+BAA+B;QACxF,OAAO;YACH,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB;QACvB,IAAI;YACA,kBAAkB;YAClB,MAAM,gBAAgB,SAAS,MAAM;YACrC,sBAAsB;gBAAE,SAAS;gBAAG,OAAO;YAAc;YAEzD,6BAA6B;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACpC,MAAM,UAAU,QAAQ,CAAC,EAAE;gBAC3B,sBAAsB;oBAAE,SAAS,IAAI;oBAAG,OAAO;gBAAc;gBAE7D,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC1B,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC,QAAQ,GAAG;YAEnB;YAEA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,MAAM,iBAAiB,4BAA4B;QACvD,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,kBAAkB;YAClB,uBAAuB;QAC3B;IACJ;IAEA,2BAA2B;IAC3B,MAAM,yBAAyB;QAC3B,IAAI;YACA,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBAC/B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACJ;YAEA,kBAAkB;YAClB,MAAM,gBAAgB,iBAAiB,MAAM;YAC7C,sBAAsB;gBAAE,SAAS;gBAAG,OAAO;YAAc;YAEzD,sCAAsC;YACtC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACpC,MAAM,YAAY,gBAAgB,CAAC,EAAE;gBACrC,sBAAsB;oBAAE,SAAS,IAAI;oBAAG,OAAO;gBAAc;gBAE7D,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAC1B,yHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,yHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;YAER;YAEA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,gBAAgB,IAAI,MAAM,GAAG,qBAAqB,CAAC;YAC5F,oBAAoB,EAAE,GAAG,kBAAkB;YAC3C,qBAAqB,QAAQ,yBAAyB;YACtD,MAAM,iBAAiB,4BAA4B;QACvD,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,qCAAqC;YACnD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,kBAAkB;YAClB,uBAAuB;QAC3B;IACJ,GAAM,2BAA2B;IACjC,MAAM,yBAAyB,CAAC;QAC5B,oBAAoB,CAAA;YAChB,IAAI,KAAK,QAAQ,CAAC,YAAY;gBAC1B,OAAO,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;YACpC,OAAO;gBACH,OAAO;uBAAI;oBAAM;iBAAU;YAC/B;QACJ;IACJ;IAEA,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,gBAAgB;QACpB;8BAAG,EAAE;IAEL,MAAM,gBAA0B;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;YAAK,UAAU;QAAW;QACpD,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,UAAU;QAAW;QAClD,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;YAAK,UAAU;QAAW;IACtD;IAEA,qBACI,6LAAC;QAAI,WAAU;;YAEV,8BAAgB,6LAAC,6IAAA,CAAA,UAAa;gBAAC,SAAQ;gBAA+B,cAAc;;;;;;YACpF,mCAAqB,6LAAC,6IAAA,CAAA,UAAa;gBAAC,SAAQ;gBAA2B,cAAc;;;;;;0BAEtF,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACD,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCACG,OAAM;oCACN,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,QAAO;8CAEP,cAAA,6LAAC;wCACG,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;gCAEJ;;;;;;;;;;;;kCAMd,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAuF;;;;;;0CAGrG,6LAAC;gCAAI,WAAU;;kDAEX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACD,MAAK;4CACL,WAAU;4CAIV,OAAO;gDACH,yBAAyB;gDACzB,aAAa;gDACb,YAAY;4CAChB;sDAEA,cAAA,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEAC/F,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEzE,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACD,MAAK;4CACL,WAAU;4CAIV,OAAO;gDACH,yBAAyB;gDACzB,aAAa;gDACb,YAAY;4CAChB;sDAEA,cAAA,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEAC/F,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEzE,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,6LAAC;;0CAEG,6LAAC;gCAAI,KAAK;gCAAS,WAAU;;kDACzB,6LAAC;wCAAG,WAAU;kDACT,iBAAiB,iBAAiB;;;;;;kDAEvC,6LAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAC9C,6LAAC;;kEACG,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAClE,6LAAC;wDACG,MAAK;wDACJ,GAAG,SAAS,OAAO;wDACpB,WAAU;wDACV,aAAY;;;;;;oDAEf,OAAO,IAAI,kBACR,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;0DAIrE,6LAAC;;kEACG,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAClE,6LAAC;wDACG,MAAK;wDACJ,GAAG,SAAS,QAAQ;wDACrB,WAAU;wDACV,aAAY;;;;;;oDAEf,OAAO,KAAK,kBACT,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAItE,6LAAC;;kEACG,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAClE,6LAAC;wDACI,GAAG,SAAS,cAAc;wDAC3B,WAAU;wDACV,aAAY;;;;;;oDAEf,OAAO,WAAW,kBACf,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0DAI5E,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAG,WAAU;kEAAgE;;;;;;oDAC7E,kCACG,6LAAC;wDAAI,WAAU;kEACX,cAAA,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;0FACX,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;6EAM/B,6LAAC;wDAAI,WAAU;kEACV,4HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,yBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAEP,YAAY;oEAAE,OAAO;gEAAK;gEAC1B,UAAU;oEAAE,OAAO;gEAAK;gEACxB,WAAW,CAAC;oDACR,EAAE,qBAAqB,SAAS,EAAE,GAC5B,wBACA,8CACL;gDACL,CAAC;gEACD,SAAS,IAAM,qBAAqB,SAAS,EAAE;;kFAE/C,6LAAC;wEAAI,WAAU;kFAA4B,SAAS,IAAI;;;;;;kFACxD,6LAAC;wEAAI,WAAU;kFACV,SAAS,IAAI;;;;;;;+DAbb,SAAS,EAAE;;;;;;;;;;;;;;;;0DAqBpC,6LAAC;;kEACG,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAClE,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEACG,WAAW,CAAC,gIAAgI,EAAE,aACxI,+BACA,cAAc,MAAM,GAAG,IACnB,4CACA,yCACJ;gEACN,aAAa,CAAC;oEACV,EAAE,cAAc;oEAChB,EAAE,eAAe;oEACjB,cAAc;gEAClB;gEACA,YAAY,CAAC;oEACT,EAAE,cAAc;oEAChB,EAAE,eAAe;oEACjB,cAAc;gEAClB;gEACA,aAAa,CAAC;oEACV,EAAE,cAAc;oEAChB,EAAE,eAAe;oEACjB,cAAc;gEAClB;gEACA,QAAQ,CAAC;oEACL,EAAE,cAAc;oEAChB,EAAE,eAAe;oEACjB,cAAc;oEAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;wEACzD,MAAM,eAAe,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;wEACpD,MAAM,iBAAiB,cAAc;wEAErC,IAAI,eAAe,MAAM,GAAG,cAAc,MAAM,EAAE;4EAC9C,iBAAiB;4EACjB,MAAM,gBAAgB,eAAe,MAAM,GAAG,cAAc,MAAM;4EAClE,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,cAAc,MAAM,EAAE,gBAAgB,IAAI,MAAM,GAAG,MAAM,CAAC;wEAC/E;oEACJ;gEACJ;gEACA,SAAS,IAAM,aAAa,OAAO,EAAE;0EAErC,cAAA,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EAAI,WAAW,CAAC,kCAAkC,EAAE,aAAa,kBAAkB,iBAAiB;4EAAE,QAAO;4EAAe,MAAK;4EAAO,SAAQ;4EAAY,eAAY;sFACrK,cAAA,6LAAC;gFAAK,GAAE;gFAAyL,aAAa;gFAAG,eAAc;gFAAQ,gBAAe;;;;;;;;;;;sFAE1P,6LAAC;4EAAI,WAAU;;8FACX,6LAAC;oFACG,MAAK;oFACL,QAAQ;oFACR,KAAK;oFACL,UAAU,CAAC;wFACP,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;4FAC7C,MAAM,qBAAqB,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;4FACpD,MAAM,iBAAiB,cAAc;4FAErC,IAAI,eAAe,MAAM,GAAG,cAAc,MAAM,EAAE;gGAC9C,iBAAiB;gGACjB,MAAM,gBAAgB,eAAe,MAAM,GAAG,cAAc,MAAM;gGAClE,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,cAAc,MAAM,EAAE,gBAAgB,IAAI,MAAM,GAAG,SAAS,CAAC;4FAClF;wFACJ;oFACJ;oFACA,WAAU;oFACV,QAAO;;;;;;8FAEX,6LAAC;oFACG,MAAK;oFACL,WAAU;oFACV,SAAS,CAAC;wFACN,EAAE,eAAe;wFACjB,aAAa,OAAO,EAAE;oFAC1B;8FACH;;;;;;8FAGD,6LAAC;oFAAE,WAAU;8FAA6B;;;;;;;;;;;;sFAE9C,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;wEACzC,cAAc,MAAM,GAAG,mBACpB,6LAAC;4EAAE,WAAU;;gFACR,cAAc,MAAM;gFAAC;gFAAO,cAAc,MAAM,GAAG,IAAI,MAAM;gFAAG;;;;;;;;;;;;;;;;;;4DAOhF,cAAc,MAAM,GAAG,mBACpB,6LAAC;gEAAI,WAAU;;oEACV,cAAc,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;4EAAgB,WAAU;;8FACvB,6LAAC;oFAAI,WAAU;;sGACX,6LAAC,gIAAA,CAAA,UAAK;4FACF,KAAK,IAAI,eAAe,CAAC;4FACzB,KAAK,CAAC,SAAS,EAAE,QAAQ,GAAG;4FAC5B,OAAO;4FACP,QAAQ;4FACR,WAAU;;;;;;sGAEd,6LAAC;4FAAI,WAAU;;;;;;;;;;;;8FAEnB,6LAAC;oFAAI,WAAU;8FACX,cAAA,6LAAC;wFACG,MAAK;wFACL,SAAS;4FACL,qCAAqC;4FACrC,iBAAiB,cAAc,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;4FACtD,wEAAwE;4FACxE,IAAI,iBAAiB,CAAC,MAAM,EAAE;gGAC1B,qBAAqB,kBAAkB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;4FAClE;4FACA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;wFAClB;wFACA,WAAU;wFACV,OAAM;kGAEN,cAAA,6LAAC;4FAAI,WAAU;4FAAU,MAAK;4FAAO,QAAO;4FAAe,SAAQ;sGAC/D,cAAA,6LAAC;gGAAK,eAAc;gGAAQ,gBAAe;gGAAQ,aAAa;gGAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8FAIjF,6LAAC;oFAAI,WAAU;8FACV,KAAK,IAAI;;;;;;;2EAhCR;;;;;oEAoCb,cAAc,MAAM,GAAG,mBACpB,6LAAC;wEACG,WAAU;wEACV,SAAS,IAAM,aAAa,OAAO,EAAE;kFAErC,cAAA,6LAAC;4EAAI,WAAU;;8FACX,6LAAC;oFAAI,WAAU;oFAAgC,QAAO;oFAAe,MAAK;oFAAO,SAAQ;8FACrF,cAAA,6LAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;;;;;;8FAEzE,6LAAC;oFAAE,WAAU;8FAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAStE,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDACG,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,YAAY,cAAc,iBAAiB,mBAAmB;;;;;;oDAGlE,+BACG,6LAAC;wDACG,MAAK;wDACL,SAAS;wDACT,WAAU;kEACb;;;;;6EAID,6LAAC;wDACG,MAAK;wDACL,SAAS;wDACT,WAAU;kEACb;;;;;;;;;;;;;;;;;;;;;;;;0CASjB,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;;kEACG,6LAAC;wDAAG,WAAU;kEAAmD;;;;;;kEACjE,6LAAC;wDAAE,WAAU;;4DAAgB;0EACT,6LAAC;gEAAK,WAAU;0EAAgC,SAAS,MAAM;;;;;;4DAC9E,mCACG,6LAAC;gEAAK,WAAU;;oEAAqB;kFACvB,6LAAC;wEAAK,WAAU;kFAAiB,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0DAKlF,6LAAC;gDAAI,WAAU;;oDAEV,SAAS,MAAM,GAAG,mBACf,6LAAC;wDACG,SAAS;4DACL,qBAAqB,CAAC;4DACtB,IAAI,mBAAmB;gEACnB,oBAAoB,EAAE;4DAC1B;wDACJ;wDACA,WAAW,CAAC;4CACR,EAAE,oBACI,gDACA,+CACJ;;0EAEN,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAU,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC/F,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DAExE,oBAAoB,mBAAmB;;;;;;;oDAK/C,qBAAqB,iBAAiB,MAAM,GAAG,mBAC5C,6LAAC;wDACG,SAAS,IAAM,uBAAuB;wDACtC,WAAU;;0EAEV,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAU,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC/F,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DACnE;4DACY,iBAAiB,MAAM;4DAAC;;;;;;;oDAKjD,SAAS,MAAM,GAAG,KAAK,CAAC,mCACrB,6LAAC;wDACG,SAAS;4DACL,oBAAoB,EAAE,GAAG,+BAA+B;4DACxD,uBAAuB;wDAC3B;wDACA,WAAU;;0EAEV,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAU,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC/F,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DACnE;;;;;;;kEAKd,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;0EAA0C;;;;;;0EAC3D,6LAAC;gEACG,WAAU;gEACV,UAAU,CAAC;oEACP,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,WAAW,SAAS;oEACrD,aAAa;oEACb;gEACJ;;kFAEA,6LAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,6LAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAK/B,6LAAC,+JAAA,CAAA,UAAI;wDACD,MAAK;wDACL,WAAU;;0EAEV,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAU,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC/F,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DACnE;;;;;;;kEAKV,6LAAC,+JAAA,CAAA,UAAI;wDACD,MAAK;wDACL,WAAU;;0EAEV,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAU,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC/F,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DACnE;;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;sDACV,SAAS,GAAG,CAAC,CAAC,wBACX,6LAAC;oDAEG,WAAW,CAAC,+GAA+G,EAAE,qBAAqB,iBAAiB,QAAQ,CAAC,QAAQ,GAAG,IACjL,yCACA,mBACA;oDACN,SAAS;wDACL,IAAI,mBAAmB;4DACnB,uBAAuB,QAAQ,GAAG;wDACtC;oDACJ;;wDAEC,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC7C,6LAAC;4DAAI,WAAU;;gEACV,QAAQ,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC,gIAAA,CAAA,UAAK;oEACF,KAAK,QAAQ,SAAS,CAAC,EAAE;oEACzB,KAAK,GAAG,QAAQ,IAAI,CAAC,EAAE,CAAC;oEACxB,OAAO;oEACP,QAAQ;oEACR,WAAU;oEACV,SAAS;wEACL,qBAAqB;wEACrB,kBAAkB,QAAQ,SAAS,CAAC,EAAE;oEAC1C;;;;;;gEAGP,QAAQ,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC;oEAAI,WAAU;;wEACV,QAAQ,SAAS,CAAC,MAAM;wEAAC;;;;;;;;;;;;;wDAIxC;sEAAoC,6LAAC;4DAAI,WAAU;;8EACjD,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;;8FACG,6LAAC;oFAAG,WAAU;8FAAmD,QAAQ,IAAI;;;;;;8FAC7E,6LAAC;oFAAE,WAAU;8FACR,4HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,QAAQ,GAAG,QAAQ;;;;;;;;;;;;wEAGrE,mCACG,6LAAC;4EACG,WAAW,CAAC,wDAAwD,EAAE,iBAAiB,QAAQ,CAAC,QAAQ,GAAG,IACrG,gCACA,mBACA;4EACN,SAAS,CAAC;gFACN,EAAE,eAAe;gFACjB,uBAAuB,QAAQ,GAAG;4EACtC;sFAEC,iBAAiB,QAAQ,CAAC,QAAQ,GAAG,mBAClC,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAqB,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FAC1G,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8EAMzF,6LAAC;oEAAE,WAAU;;wEAAgE;wEAAE,QAAQ,KAAK;;;;;;;8EAC5F,6LAAC;oEAAE,WAAU;8EAA2C,QAAQ,WAAW;;;;;;8EAC3E,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EACG,SAAS,IAAM,WAAW;4EAC1B,WAAU;sFACb;;;;;;sFAGD,6LAAC;4EACG,SAAS,IAAM,mBAAmB,QAAQ,GAAG;4EAC7C,WAAU;sFACb;;;;;;;;;;;;;;;;;;;mDAvEJ,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;0CAmFpC,6LAAC,4LAAA,CAAA,kBAAe;0CACX,gCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;8CAClB,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC,4LAAA,CAAA,kBAAe;gDAAC,SAAS;gDAAO,QAAQ;0DACrC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAEP,UAAU;oDACV,SAAQ;oDACR,SAAQ;oDACR,MAAK;oDACL,YAAY;wDAAE,UAAU;wDAAK,MAAM;oDAAY;oDAC/C,WAAU;;wDACb;wDAAqC,gCAClC,6LAAC,gIAAA,CAAA,UAAK;4DACF,KAAK;4DACL,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;mDAbT;;;;;;;;;;0DAkBb,6LAAC;gDACG,WAAU;gDACV,SAAS;0DAET,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/D,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;0DAG7E,6LAAC;gDACG,WAAU;gDACV,SAAS;0DAET,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/D,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7F,6LAAC,4LAAA,CAAA,kBAAe;0CACX,iCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;8CAClB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACP,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAI;wCACtB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,MAAM;4CAAE,OAAO;wCAAI;wCACnB,SAAS,CAAC,IAAM,EAAE,eAAe;;0DAEjC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDACG,WAAU;wDACV,SAAS,IAAM,mBAAmB;kEACrC;;;;;;kEAGD,6LAAC;wDACG,WAAU;wDACV,SAAS;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUrB,6LAAC,4LAAA,CAAA,kBAAe;0CACX,yCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;8CAClB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACP,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAI;wCACtB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,MAAM;4CAAE,OAAO;wCAAI;wCACnB,SAAS,CAAC,IAAM,EAAE,eAAe;;0DAEjC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;0DAA4B;;;;;;0DACzC,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDACG,WAAU;wDACV,SAAS,IAAM,2BAA2B;kEAC7C;;;;;;kEAGD,6LAAC;wDACG,WAAU;wDACV,SAAS;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUrB,6LAAC,4LAAA,CAAA,kBAAe;0CACX,qCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,MAAM;wCAAE,SAAS;oCAAE;oCACnB,SAAS,IAAM,CAAC,kBAAkB,uBAAuB;8CAEzD,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACP,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAI;wCACtB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,MAAM;4CAAE,OAAO;wCAAI;wCACnB,SAAS,CAAC,IAAM,EAAE,eAAe;kDAEhC,+BACG,6LAAC;4CAAI,WAAU;;8DACX,6LAAC,8IAAA,CAAA,UAAc;oDAAC,MAAK;;;;;;8DACrB,6LAAC;oDAAE,WAAU;;wDAAiC;wDACnB,mBAAmB,OAAO;wDAAC;wDAAK,mBAAmB,KAAK;wDAAC;;;;;;;8DAEpF,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDACG,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,AAAC,mBAAmB,OAAO,GAAG,mBAAmB,KAAK,GAAI,IAAI,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;iEAKhG;;8DACI,6LAAC;oDAAG,WAAU;8DACT,iBAAiB,MAAM,GAAG,IAAI,6BAA6B;;;;;;8DAEhE,6LAAC;oDAAE,WAAU;8DACR,iBAAiB,MAAM,GAAG,IACrB,2DACA;;;;;;8DAGV,6LAAC;oDAAE,WAAU;;wDAA4B;sEACpB,6LAAC;4DAAK,WAAU;;gEAC5B,iBAAiB,MAAM,GAAG,IAAI,iBAAiB,MAAM,GAAG,SAAS,MAAM;gEAAC;gEACjE,CAAC,iBAAiB,MAAM,GAAG,IAAI,iBAAiB,MAAM,GAAG,SAAS,MAAM,MAAM,IAAI,MAAM;;;;;;;wDAC7F;;;;;;;8DAEX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DACG,WAAU;4DACV,SAAS,IAAM,uBAAuB;sEACzC;;;;;;sEAGD,6LAAC;4DACG,WAAU;4DACV,SAAS,iBAAiB,MAAM,GAAG,IAAI,yBAAyB;sEAE/D,iBAAiB,MAAM,GAAG,IAAI,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavG;GA7wCM;;QA+B0E,iKAAA,CAAA,UAAO;;;KA/BjF;uCA+wCS"}}, {"offset": {"line": 2527, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}