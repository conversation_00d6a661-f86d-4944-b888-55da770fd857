<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Favicon Generator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    canvas {
      border: 1px solid #ccc;
      margin: 10px 0;
    }
    .download-links {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 20px;
    }
    .download-links a {
      display: inline-block;
      padding: 8px 12px;
      background: #1E90FF;
      color: white;
      text-decoration: none;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>Cepoka Favicon Generator</h1>
  <p>This tool generates favicon files from the SVG logo with white circular background.</p>
  
  <div>
    <h2>Original SVG</h2>
    <img src="favicon-new.svg" width="100" height="100" alt="Original SVG">
  </div>
  
  <div>
    <h2>Generated Favicons</h2>
    <canvas id="favicon16" width="16" height="16"></canvas>
    <canvas id="favicon32" width="32" height="32"></canvas>
    <canvas id="favicon192" width="192" height="192"></canvas>
    <canvas id="favicon512" width="512" height="512"></canvas>
    <canvas id="appleIcon" width="180" height="180"></canvas>
  </div>
  
  <div class="download-links" id="downloadLinks"></div>
  
  <script>
    // Function to draw the SVG on a canvas
    function drawSVGOnCanvas(canvasId, size) {
      const canvas = document.getElementById(canvasId);
      const ctx = canvas.getContext('2d');
      
      // Create a temporary image element
      const img = new Image();
      img.src = 'favicon-new.svg';
      
      // When the image loads, draw it on the canvas
      img.onload = function() {
        ctx.drawImage(img, 0, 0, size, size);
        
        // Add download link
        const downloadLink = document.createElement('a');
        downloadLink.href = canvas.toDataURL('image/png');
        downloadLink.download = `${canvasId}.png`;
        downloadLink.textContent = `Download ${canvasId}.png`;
        document.getElementById('downloadLinks').appendChild(downloadLink);
      };
    }
    
    // Generate different sizes
    window.onload = function() {
      drawSVGOnCanvas('favicon16', 16);
      drawSVGOnCanvas('favicon32', 32);
      drawSVGOnCanvas('favicon192', 192);
      drawSVGOnCanvas('favicon512', 512);
      drawSVGOnCanvas('appleIcon', 180);
    };
  </script>
</body>
</html>
