<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cepoka Favicon Generator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1 {
      color: #333;
      text-align: center;
    }
    .container {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    .preview {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin: 20px 0;
      justify-content: center;
    }
    .preview-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    .preview-item canvas {
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 10px;
      background-color: #f9f9f9;
    }
    .download-links {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 20px;
      justify-content: center;
    }
    .download-btn {
      display: inline-block;
      padding: 8px 16px;
      background: linear-gradient(to right, #1E90FF, #FF69B4);
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
      transition: transform 0.2s;
    }
    .download-btn:hover {
      transform: translateY(-2px);
    }
    .instructions {
      background-color: #f0f8ff;
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
      border-left: 4px solid #1E90FF;
    }
  </style>
</head>
<body>
  <h1>Cepoka Favicon Generator</h1>
  
  <div class="container">
    <h2>Original Logo</h2>
    <div class="preview">
      <div class="preview-item">
        <img src="/icons/sitelogo.png" width="100" height="100" alt="Original Logo" style="object-fit: contain;">
        <p>Original Logo</p>
      </div>
    </div>
  </div>
  
  <div class="container">
    <h2>Generated Favicons</h2>
    <p>These favicons have a white circular background with the logo centered:</p>
    
    <div class="preview">
      <div class="preview-item">
        <canvas id="favicon16" width="16" height="16"></canvas>
        <p>16x16</p>
      </div>
      <div class="preview-item">
        <canvas id="favicon32" width="32" height="32"></canvas>
        <p>32x32</p>
      </div>
      <div class="preview-item">
        <canvas id="favicon192" width="192" height="192"></canvas>
        <p>192x192</p>
      </div>
      <div class="preview-item">
        <canvas id="appleIcon" width="180" height="180"></canvas>
        <p>180x180 (Apple)</p>
      </div>
      <div class="preview-item">
        <canvas id="favicon512" width="512" height="512"></canvas>
        <p>512x512</p>
      </div>
    </div>
    
    <div class="download-links" id="downloadLinks"></div>
    
    <div class="instructions">
      <h3>Instructions:</h3>
      <ol>
        <li>Click the download buttons to save each favicon size</li>
        <li>Place the downloaded files in the <code>/public/icons/</code> directory</li>
        <li>The files should be named according to the download filename</li>
      </ol>
    </div>
  </div>
  
  <script>
    // Function to draw a circular favicon with the logo
    function drawFavicon(canvasId, size) {
      const canvas = document.getElementById(canvasId);
      const ctx = canvas.getContext('2d');
      
      // Draw white circular background
      ctx.fillStyle = 'white';
      ctx.beginPath();
      ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
      ctx.fill();
      
      // Add subtle shadow
      ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
      ctx.shadowBlur = size * 0.03;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
      
      // Load and draw the logo
      const logo = new Image();
      logo.src = '/icons/sitelogo.png';
      
      logo.onload = function() {
        // Calculate dimensions to maintain aspect ratio
        const logoSize = size * 0.7; // Logo takes up 70% of the favicon
        const x = (size - logoSize) / 2;
        const y = (size - logoSize) / 2;
        
        // Draw the logo
        ctx.drawImage(logo, x, y, logoSize, logoSize);
        
        // Add download link
        const filename = canvasId === 'appleIcon' 
          ? 'sitelogo-apple-touch-icon.png' 
          : `sitelogo-${canvasId}.png`;
        
        const downloadLink = document.createElement('a');
        downloadLink.href = canvas.toDataURL('image/png');
        downloadLink.download = filename;
        downloadLink.className = 'download-btn';
        downloadLink.textContent = `Download ${filename}`;
        document.getElementById('downloadLinks').appendChild(downloadLink);
      };
    }
    
    // Generate different sizes
    window.onload = function() {
      drawFavicon('favicon16', 16);
      drawFavicon('favicon32', 32);
      drawFavicon('favicon192', 192);
      drawFavicon('favicon512', 512);
      drawFavicon('appleIcon', 180);
    };
  </script>
</body>
</html>
