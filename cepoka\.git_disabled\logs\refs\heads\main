0000000000000000000000000000000000000000 be235140b33d41a6d2f8a67931a43a35551c3db8 <PERSON> <<EMAIL>> 1740363591 +0100	commit (initial): initial
be235140b33d41a6d2f8a67931a43a35551c3db8 be235140b33d41a6d2f8a67931a43a35551c3db8 Joseph <PERSON> <<EMAIL>> 1740363689 +0100	Branch: renamed refs/heads/main to refs/heads/main
be235140b33d41a6d2f8a67931a43a35551c3db8 3d2a547523802769e174379f3326f17727b7f7d2 Joseph Ani <<EMAIL>> 1740366052 +0100	commit: fixes
3d2a547523802769e174379f3326f17727b7f7d2 b8ca118f5a20c33d8407924f0f3e63ba8f56d8a1 Joseph Ani <<EMAIL>> 1740366873 +0100	commit: fix
b8ca118f5a20c33d8407924f0f3e63ba8f56d8a1 396c94bb3d3801dadcffaa4bfdd2b7f11883d5c9 Joseph Ani <<EMAIL>> 1740394748 +0100	commit: fixes
396c94bb3d3801dadcffaa4bfdd2b7f11883d5c9 64a1fb233023a24aa5ce11977a7a06a2fbe35f6a Joseph Ani <<EMAIL>> 1740395380 +0100	commit: fix
64a1fb233023a24aa5ce11977a7a06a2fbe35f6a 6aac1c0d10792ecbe9ba3ac4d308d42f8aeb8fa1 Joseph Ani <<EMAIL>> 1740396207 +0100	commit: f
6aac1c0d10792ecbe9ba3ac4d308d42f8aeb8fa1 fa8cca08e9887198e16b73a6b867ba2d19572934 Joseph Ani <<EMAIL>> 1740396781 +0100	commit: fixes
fa8cca08e9887198e16b73a6b867ba2d19572934 3d855de56b7134655981663fcb713d14b63de208 Joseph Ani <<EMAIL>> 1740397664 +0100	commit: fix
3d855de56b7134655981663fcb713d14b63de208 34aa07465f2554095860b015d29a4eb68d40e594 Joseph Ani <<EMAIL>> 1740399114 +0100	commit: fixes
34aa07465f2554095860b015d29a4eb68d40e594 2adb3dcfb5132229a7513cb7ec825eca51c3edcf Joseph Ani <<EMAIL>> 1740403789 +0100	commit: fixes
2adb3dcfb5132229a7513cb7ec825eca51c3edcf 72725b6e3935209e6aa5076378d773849e29b37b Joseph Ani <<EMAIL>> 1740405622 +0100	commit: fixes
72725b6e3935209e6aa5076378d773849e29b37b c8595d470dbe9fe77acbedf7ad14a5f5f998d042 Joseph Ani <<EMAIL>> 1740415577 +0100	commit: fixes
c8595d470dbe9fe77acbedf7ad14a5f5f998d042 109a0887720a248e30c9709c460a4c02a641be1f Joseph Ani <<EMAIL>> 1740416244 +0100	commit: more fixes
109a0887720a248e30c9709c460a4c02a641be1f f7c22e3e8a2f1873e0c2125834656a9316eaa392 Joseph Ani <<EMAIL>> 1740416327 +0100	commit: fixes
f7c22e3e8a2f1873e0c2125834656a9316eaa392 d8ae7c081ea5b177bdc29a464173523d3562ed2b Joseph Ani <<EMAIL>> 1740417054 +0100	commit: fix
d8ae7c081ea5b177bdc29a464173523d3562ed2b d14223e64abd9b8fbcfada9f8af4d85e533bea9d Joseph Ani <<EMAIL>> 1740417575 +0100	commit: fix
d14223e64abd9b8fbcfada9f8af4d85e533bea9d ab2b9855dd95c7122c30941de7a0c54c6e2120b7 Joseph Ani <<EMAIL>> 1740417926 +0100	commit: fix
ab2b9855dd95c7122c30941de7a0c54c6e2120b7 babf569a05a1f74afedb1b32feed65dd96c8da76 Joseph Ani <<EMAIL>> 1740418245 +0100	commit: fix: handle window undefined checks in components
babf569a05a1f74afedb1b32feed65dd96c8da76 d558bea0e0ee9933e834dcf47398a8447453f037 Joseph Ani <<EMAIL>> 1740419235 +0100	commit: feat: add WhatsApp button component for messaging
d558bea0e0ee9933e834dcf47398a8447453f037 167937a231bfecc495e27f8dc0d4c1560a7ee63e Joseph Ani <<EMAIL>> 1740421592 +0100	commit: feat: implement ClientMap component and enhance Map functionality
167937a231bfecc495e27f8dc0d4c1560a7ee63e e5deff9d5735245f962f1f993da9181b2c22d514 Joseph Ani <<EMAIL>> 1740425067 +0100	commit: fix: add window checks and isMounted state to prevent errors in components
e5deff9d5735245f962f1f993da9181b2c22d514 15eff05473010504cdd19de8ac58883ffe814718 Joseph Ani <<EMAIL>> 1740425677 +0100	commit: refactor: simplify ClientMap component and enhance window handling in Map component
15eff05473010504cdd19de8ac58883ffe814718 1a4212e3dbc55898572eb8ffd9e8d960b272b44d Joseph Ani <<EMAIL>> 1740432285 +0100	commit: refactor: adjust Map component height and enhance z-index handling in Nav component
1a4212e3dbc55898572eb8ffd9e8d960b272b44d 4fb8bbfa4d0c633e5a45b8639c6273f9a5716a30 Joseph Ani <<EMAIL>> 1740433297 +0100	commit: feat: enhance Hero and Nav components with overlap detection and click away functionality
4fb8bbfa4d0c633e5a45b8639c6273f9a5716a30 a68d5f3de35fa39ddebcd24baf690e5cf210179e Joseph Ani <<EMAIL>> 1740434254 +0100	commit: feat: update overlay in Hero component with blur effect for improved visual clarity
a68d5f3de35fa39ddebcd24baf690e5cf210179e d4cd9e377623770b2a850ca159ea146f3efe7fde Joseph Ani <<EMAIL>> 1740434515 +0100	commit: feat: enhance click-away functionality in Nav component for improved menu interaction
d4cd9e377623770b2a850ca159ea146f3efe7fde 430bf3acaa6b79b5932bf6a853654d94fca0ed54 Joseph Ani <<EMAIL>> 1740435984 +0100	commit: feat: update modal in WhatsAppButton with click-away functionality and enhance overlay styling
430bf3acaa6b79b5932bf6a853654d94fca0ed54 908e26019bdf359023d5af80bc29195281b467cf Joseph Ani <<EMAIL>> 1740492542 +0100	commit: feat: implement dynamic model image rotation with Framer Motion animations
908e26019bdf359023d5af80bc29195281b467cf d9a698cb0a0c5a28c5a1e9ff982ba06cb528dee3 Joseph Ani <<EMAIL>> 1740492810 +0100	commit: refactor: remove unused image in Hero component
d9a698cb0a0c5a28c5a1e9ff982ba06cb528dee3 dd699d51c5528d230a24f1302dd8f1538e25c273 Joseph Ani <<EMAIL>> 1740576584 +0100	commit: feat: add WhatsApp message functionality and enhance social media links in ContactForm
dd699d51c5528d230a24f1302dd8f1538e25c273 b4194920cfcc2bc3161cdbedcf6a6baeb0082770 Joseph Ani <<EMAIL>> 1740577468 +0100	commit: refactor: redesign Contact page layout with responsive grid and motion animations
b4194920cfcc2bc3161cdbedcf6a6baeb0082770 de9511344e06b5bc5eefad4b1b60c3db37a1f796 Joseph Ani <<EMAIL>> 1740578049 +0100	commit: refactor: update ShopContent search and filter UI with modern design and interactive elements
de9511344e06b5bc5eefad4b1b60c3db37a1f796 a64326bce3f5e59af9f04de84629490beee4db2c Joseph Ani <<EMAIL>> 1740700874 +0100	commit: feat: add admin access and location interaction in Footer component
a64326bce3f5e59af9f04de84629490beee4db2c c57c014e6f3fbfdf71357b5fb2b63380e3bc5674 Joseph Ani <<EMAIL>> 1740701386 +0100	commit: fix
c57c014e6f3fbfdf71357b5fb2b63380e3bc5674 cf4c718dff72e7edb278309b1a35b16c4bd0b7ad Joseph Ani <<EMAIL>> 1740704734 +0100	commit: feat: enhance Footer and Admin page with animated modals for admin access and image display
cf4c718dff72e7edb278309b1a35b16c4bd0b7ad 29e899ece48a33f3c1a845029ab3cae2b72645f9 Joseph Ani <<EMAIL>> 1740705363 +0100	commit: refactor: improve layout and search functionality in ShopContent, enhance z-index for WhatsAppButton
29e899ece48a33f3c1a845029ab3cae2b72645f9 11132bb6e66f875cdba0a771023d52befc91a9a5 Joseph Ani <<EMAIL>> 1740705621 +0100	commit: refactor: adjust padding in Nav component for improved layout
11132bb6e66f875cdba0a771023d52befc91a9a5 ebfa798264ae9b5c2bdffa4e174ded3515e62192 Joseph Ani <<EMAIL>> 1740706076 +0100	commit: refactor: add padding to hamburger button and update WhatsApp link in Contact page
ebfa798264ae9b5c2bdffa4e174ded3515e62192 25417b45d83837ae5303547800c6794c2114729a Joseph Ani <<EMAIL>> 1740706932 +0100	commit: refactor: update CategoryItem to include index for animation and enhance Footer admin access styling
25417b45d83837ae5303547800c6794c2114729a 260ab68ae316dd5ce3430c21a4ee422cfdcb3a06 Joseph Ani <<EMAIL>> 1740707662 +0100	commit: feat: enhance Review component with scroll-based animations and update ProductCard price format
260ab68ae316dd5ce3430c21a4ee422cfdcb3a06 d025aaa49aad5f5f19fc00dd356cbb467e4ce59a Joseph Ani <<EMAIL>> 1740708227 +0100	commit: refactor: streamline animations in Review component and optimize rendering logic
d025aaa49aad5f5f19fc00dd356cbb467e4ce59a 5a6f6f6c8879afba05887bbd7fe700416cb53fec Joseph Ani <<EMAIL>> 1740708835 +0100	commit: refactor: update input alignment in Footer, Hero, and Nav components for consistency
5a6f6f6c8879afba05887bbd7fe700416cb53fec d6e5e14cd0bf27d4866fb61b1a6bb5966d071d21 Joseph Ani <<EMAIL>> 1741037164 +0100	commit: refactor: remove border from highlight box in globals.css and add it back in Hero component for improved styling
d6e5e14cd0bf27d4866fb61b1a6bb5966d071d21 1e999c5e94ef5d829314eced10dca5601ee74b75 Joseph Ani <<EMAIL>> 1741037554 +0100	commit: feat: add image count display and enhance navigation button styles in AdminPage
1e999c5e94ef5d829314eced10dca5601ee74b75 4974a27430a8eb5dcb39b70e773aaccb8915c0bd Joseph Ani <<EMAIL>> 1741037816 +0100	commit: feat: add delete confirmation modal in AdminPage for product deletion
4974a27430a8eb5dcb39b70e773aaccb8915c0bd e2b30aaa019224d54884d76dc03230dc535ec91d Joseph Ani <<EMAIL>> 1741038007 +0100	commit: feat: add keyboard accessibility for admin access in Footer component
e2b30aaa019224d54884d76dc03230dc535ec91d bc488f30771bedf38f8e9f301a3875420f3afaff Joseph Ani <<EMAIL>> 1741038781 +0100	commit: feat: integrate react-swipeable for image navigation in AdminPage
bc488f30771bedf38f8e9f301a3875420f3afaff 84cb4b0d1d4d624311675c746d06e2d6f584bfb0 Joseph Ani <<EMAIL>> 1741039255 +0100	commit: feat: add color selection buttons in AdminPage for enhanced product customization
84cb4b0d1d4d624311675c746d06e2d6f584bfb0 78f39e54699d5c5d2d69b723ffc51b16ea0a238f Joseph Ani <<EMAIL>> 1741039831 +0100	commit: feat: enhance color selection button to prevent default event behavior
78f39e54699d5c5d2d69b723ffc51b16ea0a238f 69bdb14ab2fccc7f5b44d41625e2abe502f0038f Joseph Ani <<EMAIL>> 1741043104 +0100	commit: feat: implement animated image transitions in AdminPage using framer-motion
69bdb14ab2fccc7f5b44d41625e2abe502f0038f 24d4695da2e524b84343c72b7439eeb42be1260e Joseph Ani <<EMAIL>> 1741379087 +0100	commit: feat: configure Next.js for CORS and Appwrite integration; update ProductCard to handle multiple images
24d4695da2e524b84343c72b7439eeb42be1260e 6c3a859dac695fe5187243b32627b301f87200a6 Joseph Ani <<EMAIL>> 1741385951 +0100	commit: feat: add ESLint configuration; refactor product interface and shop data export; improve footer functionality and error handling
6c3a859dac695fe5187243b32627b301f87200a6 af7e725f889738e1c30989b29a1255980f22fb0d Joseph Ani <<EMAIL>> 1741390694 +0100	commit: feat: update AboutFugo component with brand story; enhance customer reviews with new testimonials
af7e725f889738e1c30989b29a1255980f22fb0d 5218f1fbc3f6bf7de4391eca8fa8a688d9c02494 Joseph Ani <<EMAIL>> 1741391158 +0100	commit: fix: correct apostrophe in AboutFugo component description
5218f1fbc3f6bf7de4391eca8fa8a688d9c02494 d8a362b5ad8e2fd15ebc388ca5696a62e3ab6035 Joseph Ani <<EMAIL>> 1741545411 +0100	commit: feat: add category filtering to ShopContent; enhance Categories component with animations and improved props interface
d8a362b5ad8e2fd15ebc388ca5696a62e3ab6035 ea9a5842acf5cad0d21a36f2e62dcf21dbc927ca Joseph Ani <<EMAIL>> 1741546266 +0100	commit: feat: enhance Map component for mobile usability; disable dragging and zooming on mobile devices
ea9a5842acf5cad0d21a36f2e62dcf21dbc927ca c3c99c81d77f39716d3713cae88e9bb9ea7b9e1e Joseph Ani <<EMAIL>> 1741546602 +0100	commit: feat: improve Map component responsiveness; update icon paths and handle window resize events
c3c99c81d77f39716d3713cae88e9bb9ea7b9e1e a44e70c45e9fa7e7e98d811c0ad7d21af2641e94 Joseph Ani <<EMAIL>> 1741546784 +0100	commit: fix: safely handle tap property in Map component; ensure compatibility with map object
a44e70c45e9fa7e7e98d811c0ad7d21af2641e94 74efd7f1ee32cb55cb50a89277840fa64c22c36f Joseph Ani <<EMAIL>> 1741547333 +0100	commit: feat: extend Map component to safely handle tap property and improve type definitions
74efd7f1ee32cb55cb50a89277840fa64c22c36f 8b302415d3b08d6b945f3fdd103f0c50849577bc Joseph Ani <<EMAIL>> 1741548123 +0100	commit: fix: update icon paths in Map component for consistency and improved asset management
8b302415d3b08d6b945f3fdd103f0c50849577bc 404c3a9cb0d58de89c373683e78a7da43b6ef3b1 Joseph Ani <<EMAIL>> 1741548875 +0100	commit: fix: adjust padding and spacing in HomePage component for improved layout consistency
404c3a9cb0d58de89c373683e78a7da43b6ef3b1 f5c8bcb6701c8bef7f0da2b7842bfccda4829c3e Joseph Ani <<EMAIL>> 1741549545 +0100	commit: fix: refactor HomePage layout for improved structure and readability
f5c8bcb6701c8bef7f0da2b7842bfccda4829c3e 284f1caee20ea544ed51898f0f26c072cf587938 Joseph Ani <<EMAIL>> 1741549718 +0100	commit: fix: remove unused Nav import and adjust layout for consistent spacing
284f1caee20ea544ed51898f0f26c072cf587938 48cf2b3a4dba917459516d5d37583875ca7d01f1 Joseph Ani <<EMAIL>> 1741622060 +0100	commit: fix: update comments and enhance thumbnail gallery functionality in ProductPageClient
48cf2b3a4dba917459516d5d37583875ca7d01f1 fa48f76a4f91fb9e9abe40c9327078a5879d833f Joseph Ani <<EMAIL>> 1741622296 +0100	commit: fix: remove unused Heart import and clean up ProductPageClient component
fa48f76a4f91fb9e9abe40c9327078a5879d833f 082d320a5dd11e0392bf2d484d0c10ab5f0ca193 Joseph Ani <<EMAIL>> 1741622748 +0100	commit: fix: remove unnecessary overflow property from thumbnail gallery container in ProductPageClient
082d320a5dd11e0392bf2d484d0c10ab5f0ca193 b5bc34debcc3bffcb9d9e06b8aec9bdd789a4ea2 Joseph Ani <<EMAIL>> 1741623484 +0100	commit: fix: enhance WhatsAppButton component with improved styling and functionality
b5bc34debcc3bffcb9d9e06b8aec9bdd789a4ea2 f0e315c09f5001e2877d510ae3367ed742d05919 Joseph Ani <<EMAIL>> 1741789055 +0100	commit: fix: enhance product display components with improved image handling and layout adjustments
f0e315c09f5001e2877d510ae3367ed742d05919 86bb467ef50aca6e14ea7f4715ef95fa074a3b4d Joseph Ani <<EMAIL>> 1741789212 +0100	commit: fix: remove unused TrendingUp import from LatestProduct component
86bb467ef50aca6e14ea7f4715ef95fa074a3b4d e0038eaa9272beaa9d39d41a625e5c854ec160d2 Joseph Ani <<EMAIL>> 1741817462 +0100	commit: fix: add sharp package and improve animation handling in Offer and Review components
e0038eaa9272beaa9d39d41a625e5c854ec160d2 c8dc447aa0f1e8577f6bcac7ba4345eee86b9e46 Joseph Ani <<EMAIL>> 1741817660 +0100	commit: fix: enable WhatsAppButton component in HomePage
c8dc447aa0f1e8577f6bcac7ba4345eee86b9e46 954754c49c3480937b057f1fd7e6635ac49fd4d5 Joseph Ani <<EMAIL>> 1741818298 +0100	commit: fix: adjust dimensions and positioning in OpeningHours component for better layout
954754c49c3480937b057f1fd7e6635ac49fd4d5 ca7325e73bc4a99b0b2bf96d0f01d754ef21d246 Joseph Ani <<EMAIL>> 1741819839 +0100	commit: fix: adjust image scaling and positioning in OpeningHours component for improved layout
ca7325e73bc4a99b0b2bf96d0f01d754ef21d246 95322157c931168348c2879622713c23bc395dc5 Joseph Ani <<EMAIL>> 1741821286 +0100	commit: fix: add current day highlighting and animation to OpeningHours component
95322157c931168348c2879622713c23bc395dc5 6bb6d4bbb0a4de332b0ad7fa48b4f744bf9071b4 Joseph Ani <<EMAIL>> 1742735916 +0100	commit: fix: update images and icons for improved visual consistency and layout
6bb6d4bbb0a4de332b0ad7fa48b4f744bf9071b4 31d305d0f37d2d855b08b32f60245201b410cb10 Joseph Ani <<EMAIL>> 1742736286 +0100	commit: fix: remove unused imports in BestSellers component for cleaner code
31d305d0f37d2d855b08b32f60245201b410cb10 ba597606ecb0f9c61fa76f26ad288ea7cc3b0091 Joseph Ani <<EMAIL>> 1742738248 +0100	commit: fix: update styling in BestSellers component for improved layout and visual consistency
ba597606ecb0f9c61fa76f26ad288ea7cc3b0091 2909df8c7e8347b50e65273aade0bad25e3379e7 Joseph Ani <<EMAIL>> 1742738517 +0100	commit: fix: adjust padding and width in BestSellers component for improved layout
2909df8c7e8347b50e65273aade0bad25e3379e7 4099e65c2a7e28222b5046b65114ed69fe3ea7e8 Joseph Ani <<EMAIL>> 1742739619 +0100	commit: fix: simplify header components and unify back button styling across pages
4099e65c2a7e28222b5046b65114ed69fe3ea7e8 f8615ae034cd7cbacbfd4aade35c881d5234da8a Joseph Ani <<EMAIL>> 1742739832 +0100	commit: fix: remove unused products import in ShopContent component for cleaner code
f8615ae034cd7cbacbfd4aade35c881d5234da8a 3e4007c192d0b98895450971b45756d6f512fc75 Joseph Ani <<EMAIL>> 1742740044 +0100	commit: fix: clean up imports and comment out unused handleBack function in ShopContent component
3e4007c192d0b98895450971b45756d6f512fc75 c0f22412c5a5fafbe1e4cb76562adecc43ae926c Joseph Ani <<EMAIL>> 1742797652 +0100	commit: fix: adjust logo size in Nav component for better alignment and consistency
c0f22412c5a5fafbe1e4cb76562adecc43ae926c e00c1f6e6ee4e08dd99320c6c8b25c507cbaedfa Joseph Ani <<EMAIL>> 1742797981 +0100	commit: fix: adjust logo dimensions in Hero component for better responsiveness
e00c1f6e6ee4e08dd99320c6c8b25c507cbaedfa 9eb50dc2b9c45834c613fa1e256927b3639c3fcc Joseph Ani <<EMAIL>> 1742798821 +0100	commit: fix: adjust logo dimensions in Hero component for improved display
9eb50dc2b9c45834c613fa1e256927b3639c3fcc 8f574d40be3d2bb1cd5ee73262e28ea87940de0b Joseph Ani <<EMAIL>> 1742804697 +0100	commit: fix: update model image paths and adjust styling in Hero component for improved layout
8f574d40be3d2bb1cd5ee73262e28ea87940de0b 831c569ef6358b679b8dd23d0a5996d55b60abf7 Joseph Ani <<EMAIL>> 1742805050 +0100	commit: fix: adjust padding in Hero component for improved layout
831c569ef6358b679b8dd23d0a5996d55b60abf7 954ccf2c5893dee8e76022185cfc92bc136d5c7f Joseph Ani <<EMAIL>> 1742805266 +0100	commit: fix: adjust padding in Hero component for improved text alignment
954ccf2c5893dee8e76022185cfc92bc136d5c7f 6bcd879da04abc0ba8204bc415b25a4d81f5b0cd Joseph Ani <<EMAIL>> 1742805517 +0100	commit: fix: adjust padding in Hero component for improved text positioning
6bcd879da04abc0ba8204bc415b25a4d81f5b0cd 14b187769888c846bf225ee29bb4a0ae7a234f54 Joseph Ani <<EMAIL>> 1742805635 +0100	commit: fix: adjust padding in Hero component for improved layout consistency
14b187769888c846bf225ee29bb4a0ae7a234f54 faf2378d79b2bb1419e3193fd57b195325717d0d Joseph Ani <<EMAIL>> 1742806334 +0100	commit: fix: update gradient background height and adjust spacing in BestSellers component
faf2378d79b2bb1419e3193fd57b195325717d0d 93a633cf6891c0ce66d3836318e93734e69dcfed Joseph Ani <<EMAIL>> 1742807322 +0100	commit: fix: remove unnecessary id from section 3 for cleaner markup
93a633cf6891c0ce66d3836318e93734e69dcfed a91cedc0bcf7fdb19a75231a4e4fc5228e3292ef Joseph Ani <<EMAIL>> 1743122977 +0100	commit: feat: implement responsive design adjustments and add custom media query hook
a91cedc0bcf7fdb19a75231a4e4fc5228e3292ef 99ab90cdddd0532aa28d53551a03c0ee1f254a14 Joseph Ani <<EMAIL>> 1743123149 +0100	commit: fix: correct typo in rounded class for ContactForm component
