@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/ClashDisplay-Variable.woff2') format('woff2-variations'),
    url('/fonts/ClashDisplay-Variable.woff') format('woff-variations'),
    url('/fonts/ClashDisplay-Variable.ttf') format('truetype-variations');
  font-weight: 200 700;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;

}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;

}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Clash Display', sans-serif;
}

.highlight-box {
  background: #c4c4c434;
  backdrop-filter: blur(0.8em);
  position: absolute;
  top: 10px;
  right: -40px;
  margin-left: 30px;
}


.fade-boundary {
  background-image: linear-gradient(to top, #ffffff, #11111100);
  position: absolute;
  width: 100%;
  height: 70px;
  bottom: -1px;
  z-index: 10;
}

.hero {
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  display: block;
  position: absolute;
  top: -75px;
  /* Positioned at edge */
  right: -75px;
  /* Positioned at edge */
  background: radial-gradient(circle,
      rgba(0, 115, 255, 0.8) 0%,
      rgba(0, 115, 255, 0.4) 30%,
      rgba(0, 115, 255, 0) 70%);
  width: 200px;
  height: 200px;

  @media (min-width: 768px) {
    width: 300px;
    height: 300px;
  }

  height: 300px;
  border-radius: 50%;
  filter: blur(50px);
  animation: breatheEffect 6s ease-in-out infinite reverse;
  opacity: 0.8;
  transform-origin: center;
  pointer-events: none;


}

.hero::after {


  content: "";
  display: block;
  position: absolute;
  bottom: -75px;
  /* Positioned at edge */
  left: -75px;
  /* Positioned at edge */
  background: radial-gradient(circle,
      rgba(234, 136, 254, 0.8) 0%,
      rgba(234, 136, 254, 0.4) 30%,
      rgba(234, 136, 254, 0) 70%);
  width: 200px;
  height: 200px;

  @media (min-width: 768px) {
    width: 300px;
    height: 300px;
  }

  border-radius: 50%;
  filter: blur(50px);
  animation: breatheEffect 6s ease-in-out infinite;
  opacity: 0.8;
  transform-origin: center;
  pointer-events: none;
}

@keyframes breatheEffect {
  0% {
    transform: scale(1);
    opacity: 0.8;
    filter: blur(50px);
  }

  50% {
    transform: scale(1.3);
    opacity: 0.6;
    filter: blur(60px);
  }

  100% {
    transform: scale(1);
    opacity: 0.8;
    filter: blur(50px);
  }
}

.price-card {
  background: #9696967a;
  backdrop-filter: blur(0.3em);
}


.latest-product-card {
  background: linear-gradient(to bottom, #dddddd, #b2b2b2);
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #1E90FF, #FF69B4);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #0077e6, #ff4aa5);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #1E90FF #f1f1f1;
}