{"name": "d-fugo-hair", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export", "deploy": "npm run build && npm run export"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^4.1.2", "@react-google-maps/api": "^2.20.5", "@types/google.maps": "^3.58.1", "@uploadthing/react": "^7.3.0", "appwrite": "^17.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lucide-react": "^0.475.0", "next": "15.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.14.1", "react-leaflet": "^5.0.0", "react-swipeable": "^7.0.2", "uploadthing": "^7.5.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/leaflet": "^1.9.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "sharp": "^0.34.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}