{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/Components/SpinningLoader.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface SpinningLoaderProps {\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  text?: string;\n}\n\nconst SpinningLoader: React.FC<SpinningLoaderProps> = ({ \n  size = 'medium', \n  className = '',\n  text\n}) => {\n  // Size mapping\n  const sizeMap = {\n    small: 'w-6 h-6 border-2',\n    medium: 'w-10 h-10 border-3',\n    large: 'w-16 h-16 border-4',\n  };\n\n  const sizeClass = sizeMap[size];\n  \n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <motion.div\n        className={`${sizeClass} rounded-full border-t-pink-500 border-r-blue-500 border-b-pink-500 border-l-blue-500`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1.5,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n        style={{ borderStyle: 'solid' }}\n      />\n      {text && (\n        <p className=\"mt-3 text-sm text-gray-600 font-medium\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default SpinningLoader;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,IAAI,EACL;IACC,eAAe;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,CAAC,KAAK;IAE/B,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,UAAU,qFAAqF,CAAC;gBAC9G,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,aAAa;gBAAQ;;;;;;YAE/B,sBACC,8OAAC;gBAAE,WAAU;0BAA0C;;;;;;;;;;;;AAI/D;uCAEe"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/components/ProductCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport React, { useRef, useState } from \"react\";\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { Heart } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\nimport SpinningLoader from \"@/src/app/Components/SpinningLoader\";\r\n\r\n// Match the Appwrite data structure\r\ninterface Product {\r\n  $id: string;\r\n  name: string;\r\n  price: string;\r\n  description: string;\r\n  imageUrls: string[];\r\n}\r\n\r\ninterface ProductCardProps {\r\n  product: Product;\r\n}\r\n\r\nconst cardVariants = {\r\n  hidden: { opacity: 0, y: 20 },\r\n  visible: {\r\n    opacity: 1,\r\n    y: 0,\r\n    transition: { duration: 0.4 }\r\n  },\r\n  hover: {\r\n    y: -5,\r\n    transition: { duration: 0.2 }\r\n  },\r\n  tap: { scale: 0.98 }\r\n};\r\n\r\nconst likeColour = {\r\n  on: \"#ff3b5c\",\r\n  off: \"#ffffff50\"\r\n};\r\n\r\nexport const ProductCard: React.FC<ProductCardProps> = ({ product }) => {\r\n  const [isLiked, setIsLiked] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const ref = useRef(null);\r\n  const inView = useInView(ref);\r\n  const router = useRouter();\r\n\r\n  const handleProductClick = () => {\r\n    try {\r\n      // Show loading screen\r\n      setIsLoading(true);\r\n\r\n      // Save product data to localStorage\r\n      const slug = product.name.toLowerCase().replace(/\\s+/g, \"-\");\r\n      const productData = {\r\n        name: product.name,\r\n        price: product.price,\r\n        description: product.description,\r\n        imageUrls: product.imageUrls\r\n      };\r\n      localStorage.setItem(\"selectedProduct\", JSON.stringify(productData));\r\n\r\n      // Navigate to product page after a delay to show loading screen\r\n      // Using a longer delay on mobile for better visibility\r\n      const isMobile = window.innerWidth < 768;\r\n      setTimeout(() => {\r\n        router.push(`/product/${slug}`);\r\n      }, isMobile ? 1200 : 800);\r\n    } catch (error) {\r\n      console.error(\"Error saving product data:\", error);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Loading Screen with animation */}\r\n      {isLoading && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\"\r\n        >\r\n          <div className=\"bg-white rounded-lg p-6 shadow-xl\">\r\n            <SpinningLoader size=\"large\" text={`Loading ${product.name}...`} />\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      <motion.div\r\n        ref={ref}\r\n        onClick={handleProductClick}\r\n        className=\"latest-product-card w-[160px] h-[220px] sm:w-[220px] sm:h-[280px] flex flex-col items-center\r\n        relative rounded-[15px] sm:rounded-[25px] cursor-pointer transition-colors duration-200 overflow-hidden\"\r\n        initial=\"hidden\"\r\n        animate={inView ? \"visible\" : \"hidden\"}\r\n        whileHover=\"hover\"\r\n        whileTap=\"tap\"\r\n        variants={cardVariants}\r\n        style={{\r\n          backfaceVisibility: \"hidden\",\r\n          WebkitFontSmoothing: \"subpixel-antialiased\"\r\n        }}\r\n      >\r\n        {/* Product Image */}\r\n        <div className=\"w-full h-full relative\">\r\n          {product.imageUrls && product.imageUrls.length > 0 ? (\r\n            <Image\r\n              className=\"object-cover\"\r\n              fill\r\n              alt={product.name || \"Product image\"}\r\n              src={product.imageUrls[0]}\r\n              unoptimized={true}\r\n              sizes=\"(max-width: 640px) 160px, 220px\"\r\n              style={{ objectFit: 'cover' }}\r\n            />\r\n          ) : (\r\n            <div className=\"w-full h-full flex items-center justify-center bg-gray-200\">\r\n              <p className=\"text-gray-400\">No Image</p>\r\n            </div>\r\n          )}\r\n          {/* Gradient Overlay */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/10 to-transparent\" />\r\n        </div>\r\n\r\n        {/* Heart icon */}\r\n        <div\r\n          onClick={(e) => {\r\n            e.preventDefault();\r\n            e.stopPropagation();\r\n            setIsLiked(!isLiked);\r\n          }}\r\n          className=\"absolute right-2 sm:right-4 top-2 sm:top-4 z-10 p-1 sm:p-2 cursor-pointer hover:scale-110 transition-transform\"\r\n        >\r\n          <Heart\r\n            className=\"stroke-none\"\r\n            fill={isLiked ? likeColour.on : likeColour.off}\r\n            size={24}\r\n            strokeWidth={1}\r\n          />\r\n        </div>\r\n\r\n        {/* Price Card */}\r\n        <div className=\"price-card w-[90%] h-[70px] sm:h-[80px] rounded-[10px] sm:rounded-[15px]\r\n      absolute bottom-3 bg-gradient-to-r from-black/80 to-black/40 backdrop-blur-[2px]\r\n      flex flex-col justify-center gap-1 sm:gap-2\">\r\n          <div className=\"px-3 sm:px-4 text-white font-semibold text-xs sm:text-sm truncate\">\r\n            {product.name}\r\n          </div>\r\n          <div className=\"w-full h-[1px] bg-[#dddd]\"></div>\r\n          <div className=\"flex items-center justify-between px-3 sm:px-4\">\r\n            <p className=\"text-white font-medium text-xs sm:text-sm\">₦{product.price}</p>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA;AACA;AAJA;AAAA;AACA;AAHA;;;;;;;;AAqBA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,OAAO;QACL,GAAG,CAAC;QACJ,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,KAAK;QAAE,OAAO;IAAK;AACrB;AAEA,MAAM,aAAa;IACjB,IAAI;IACJ,KAAK;AACP;AAEO,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,qBAAqB;QACzB,IAAI;YACF,sBAAsB;YACtB,aAAa;YAEb,oCAAoC;YACpC,MAAM,OAAO,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;YACxD,MAAM,cAAc;gBAClB,MAAM,QAAQ,IAAI;gBAClB,OAAO,QAAQ,KAAK;gBACpB,aAAa,QAAQ,WAAW;gBAChC,WAAW,QAAQ,SAAS;YAC9B;YACA,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YAEvD,gEAAgE;YAChE,uDAAuD;YACvD,MAAM,WAAW,OAAO,UAAU,GAAG;YACrC,WAAW;gBACT,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM;YAChC,GAAG,WAAW,OAAO;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,aAAa;QACf;IACF;IAEA,qBACE;;YAEG,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2IAAA,CAAA,UAAc;wBAAC,MAAK;wBAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;0BAKrE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,KAAK;gBACL,SAAS;gBACT,WAAU;gBAEV,SAAQ;gBACR,SAAS,SAAS,YAAY;gBAC9B,YAAW;gBACX,UAAS;gBACT,UAAU;gBACV,OAAO;oBACL,oBAAoB;oBACpB,qBAAqB;gBACvB;;kCAGA,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,kBAC/C,8OAAC,6HAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,IAAI;gCACJ,KAAK,QAAQ,IAAI,IAAI;gCACrB,KAAK,QAAQ,SAAS,CAAC,EAAE;gCACzB,aAAa;gCACb,OAAM;gCACN,OAAO;oCAAE,WAAW;gCAAQ;;;;;qDAG9B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;0CAIjC,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBACC,SAAS,CAAC;4BACR,EAAE,cAAc;4BAChB,EAAE,eAAe;4BACjB,WAAW,CAAC;wBACd;wBACA,WAAU;kCAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BACJ,WAAU;4BACV,MAAM,UAAU,WAAW,EAAE,GAAG,WAAW,GAAG;4BAC9C,MAAM;4BACN,aAAa;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAA4C;wCAAE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpF"}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/lib/appwrite.ts"], "sourcesContent": ["import { Client, Account, Databases, Storage } from \"appwrite\";\r\n\r\n// Initialize the Appwrite client\r\nconst client = new Client()\r\n  .setEndpoint(\"https://cloud.appwrite.io/v1\")\r\n  .setProject(\"67d07dc9000bafdd5d81\"); // Confirmed correct project ID\r\n\r\nexport const account = new Account(client);\r\nexport const databases = new Databases(client);\r\nexport const storage = new Storage(client);\r\n\r\nexport const appwriteConfig = {\r\n  // Using the confirmed database ID\r\n  databaseId: \"6813eadb003e7d64f63c\",\r\n  productsCollectionId: \"6813eaf40036e52c29b1\",\r\n  categoriesCollectionId: \"6817640f000dd0b67c77\",\r\n  stockProductsCollectionId: \"681a651d001cc3de8395\",\r\n  stockMovementsCollectionId: \"681bddcc000204a3748d\",\r\n  storageId: \"6813ea36001624c1202a\",\r\n};\r\n\r\n// project id: 67d07d7b0010f39ec77d\r\n// database id: 67d8833d000778157021\r\n// collection id: 67d8835b002502c5d7ba\r\n// storage id: 67d8841a001213adf116\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,iCAAiC;AACjC,MAAM,SAAS,IAAI,8IAAA,CAAA,SAAM,GACtB,WAAW,CAAC,gCACZ,UAAU,CAAC,yBAAyB,+BAA+B;AAE/D,MAAM,UAAU,IAAI,8IAAA,CAAA,UAAO,CAAC;AAC5B,MAAM,YAAY,IAAI,8IAAA,CAAA,YAAS,CAAC;AAChC,MAAM,UAAU,IAAI,8IAAA,CAAA,UAAO,CAAC;AAE5B,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,YAAY;IACZ,sBAAsB;IACtB,wBAAwB;IACxB,2BAA2B;IAC3B,4BAA4B;IAC5B,WAAW;AACb,GAEA,mCAAmC;CACnC,oCAAoC;CACpC,sCAAsC;CACtC,mCAAmC"}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/data/categories.ts"], "sourcesContent": ["export interface Category {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  imageSrc: string;\r\n}\r\n\r\nexport const CATEGORIES: Category[] = [\r\n  {\r\n    id: \"spa-salon-furniture\",\r\n    name: \"Spa and salon furnitures\",\r\n    icon: \"🪑\",\r\n    imageSrc: \"/icons/spa-bed.png\",\r\n  },\r\n  {\r\n    id: \"beauty-equipment\",\r\n    name: \"Beauty equipment\",\r\n    icon: \"⚙️\",\r\n    imageSrc: \"/icons/hairdryer.png\",\r\n  },\r\n  {\r\n    id: \"facial-waxing\",\r\n    name: \"Facials and waxing\",\r\n    icon: \"🧖‍♀️\",\r\n    imageSrc: \"/icons/hot-stone.png\",\r\n  },\r\n  {\r\n    id: \"skincare-accessories\",\r\n    name: \"Skincare products & accessories\",\r\n    icon: \"🧴\",\r\n    imageSrc: \"/icons/slim.png\",\r\n  },\r\n  {\r\n    id: \"pedicure-manicure\",\r\n    name: \"Pedicure and manicure\",\r\n    icon: \"💅\",\r\n    imageSrc: \"/icons/nails.png\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AAOO,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;IACZ;CACD"}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/shop/ShopContent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport SpinningLoader from \"../Components/SpinningLoader\";\r\nimport { useState, useMemo, useEffect } from \"react\";\r\nimport { ProductCard } from \"@/src/components/ProductCard\";\r\nimport { databases, appwriteConfig } from \"@/src/lib/appwrite\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { CATEGORIES } from '@/src/data/categories';\r\nimport { Query } from 'appwrite';\r\nimport Link from 'next/link';\r\n\r\ninterface Product {\r\n  $id: string;\r\n  name: string;\r\n  price: string;\r\n  description: string;\r\n  imageUrls: string[];\r\n  category?: string;\r\n  categoryName?: string; // Add category name field\r\n  $createdAt: string;\r\n}\r\n\r\nexport default function ShopContent() {\r\n  const searchParams = useSearchParams();\r\n  const initialSearchQuery = searchParams ? searchParams.get('search') || \"\" : \"\";\r\n  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);\r\n  const [activeCategory, setActiveCategory] = useState<string | null>(null);\r\n  const [showScrollTop, setShowScrollTop] = useState(false);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const [products, setProducts] = useState<Product[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');\r\n\r\n  // Component Mount Handler\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  // Scroll Handler\r\n  useEffect(() => {\r\n    if (!isMounted) return;\r\n\r\n    const handleScroll = () => {\r\n      if (typeof window !== 'undefined') {\r\n        setShowScrollTop(window.scrollY > 500);\r\n      }\r\n    };\r\n\r\n    window?.addEventListener('scroll', handleScroll);\r\n    return () => window?.removeEventListener('scroll', handleScroll);\r\n  }, [isMounted]);\r\n\r\n  // URL Params Handler\r\n  useEffect(() => {\r\n    if (!searchParams || !isMounted) return;\r\n\r\n    const categoryId = searchParams.get('category');\r\n    const shouldSelect = searchParams.get('select') === 'true';\r\n    const searchParam = searchParams.get('search');\r\n\r\n    console.log('URL Parameters:', { categoryId, shouldSelect, searchParam });\r\n\r\n    if (categoryId && shouldSelect) {\r\n      console.log('Setting active category:', categoryId);\r\n      setActiveCategory(categoryId);\r\n\r\n      // Clean up URL after setting category\r\n      const url = new URL(window.location.href);\r\n      url.searchParams.delete('select');\r\n      window.history.replaceState({}, '', url.toString());\r\n    }\r\n\r\n    if (searchParam) {\r\n      console.log('Setting search query:', searchParam);\r\n      setSearchQuery(searchParam);\r\n    }\r\n  }, [searchParams, isMounted]);\r\n\r\n  // Fetch products from Appwrite - fetch ALL products with pagination\r\n  useEffect(() => {\r\n    const fetchProducts = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        // First request with limit=100 (Appwrite's maximum)\r\n        const response = await databases.listDocuments(\r\n          appwriteConfig.databaseId,\r\n          appwriteConfig.productsCollectionId,\r\n          [\r\n            Query.limit(100), // Get 100 documents per request (maximum)\r\n            Query.offset(0),  // Start from the first document\r\n            Query.orderDesc('$createdAt') // Default to newest first\r\n          ]\r\n        );\r\n\r\n        // Initialize our products array with the first batch\r\n        let allDocuments = [...response.documents];\r\n\r\n        // If there are more documents than the limit, fetch them with pagination\r\n        if (response.total > 100) {\r\n          // Calculate how many more requests we need\r\n          const totalRequests = Math.ceil(response.total / 100);\r\n\r\n          // Make additional requests to get all documents\r\n          for (let i = 1; i < totalRequests; i++) {\r\n            const offset = i * 100;\r\n            const additionalResponse = await databases.listDocuments(\r\n              appwriteConfig.databaseId,\r\n              appwriteConfig.productsCollectionId,\r\n              [\r\n                Query.limit(100),\r\n                Query.offset(offset),\r\n                Query.orderDesc('$createdAt')\r\n              ]\r\n            );\r\n\r\n            // Add these documents to our array\r\n            allDocuments = [...allDocuments, ...additionalResponse.documents];\r\n          }\r\n        }\r\n\r\n        console.log('✅ Fetched ALL Products:', allDocuments.length, 'total products');\r\n        const productsData = allDocuments as unknown as Product[];\r\n        setProducts(productsData);\r\n      } catch (error) {\r\n        console.error('❌ Error fetching products:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchProducts();\r\n  }, []);\r\n\r\n  // Filter products\r\n  const filteredProducts = useMemo(() => {\r\n    const filtered = products.filter(product => {\r\n      // Match by category ID (using the predefined category IDs from CATEGORIES)\r\n      const matchesCategory = !activeCategory ||\r\n        product.category === activeCategory ||\r\n        product.categoryName === CATEGORIES.find(cat => cat.id === activeCategory)?.name;\r\n\r\n      // Debug logging for category matching\r\n      console.log(`Product \"${product.name}\":\r\n        - Category ID: ${product.category || 'none'}\r\n        - Category Name: ${product.categoryName || 'none'}\r\n        - Active Category: ${activeCategory}\r\n        - Active Category Name: ${CATEGORIES.find(cat => cat.id === activeCategory)?.name || 'none'}\r\n        - Matches Category: ${matchesCategory}\r\n      `);\r\n\r\n      const matchesSearch = !searchQuery ||\r\n        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        product.description.toLowerCase().includes(searchQuery.toLowerCase());\r\n\r\n      return matchesCategory && matchesSearch;\r\n    });\r\n\r\n    // Sort products by date\r\n    filtered.sort((a, b) => {\r\n      const dateA = new Date(a.$createdAt).getTime();\r\n      const dateB = new Date(b.$createdAt).getTime();\r\n      return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;\r\n    });\r\n\r\n    return filtered;\r\n  }, [products, activeCategory, searchQuery, sortOrder]);\r\n\r\n  // Search handler\r\n  const handleSearch = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!isMounted || typeof window === 'undefined') return;\r\n\r\n    try {\r\n      const url = new URL(window.location.href);\r\n      if (searchQuery) {\r\n        url.searchParams.set('search', searchQuery);\r\n      } else {\r\n        url.searchParams.delete('search');\r\n      }\r\n      if (activeCategory) {\r\n        url.searchParams.set('category', activeCategory);\r\n      }\r\n      window.history.pushState({}, '', url.toString());\r\n    } catch (error) {\r\n      console.error('Error updating URL:', error);\r\n    }\r\n  };\r\n\r\n  // Scroll to top handler\r\n  const scrollToTop = () => {\r\n    if (!isMounted || typeof window === 'undefined') return;\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        className=\"min-h-screen flex items-center justify-center\"\r\n      >\r\n        <SpinningLoader size=\"large\" text=\"Loading products...\" />\r\n      </motion.div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"text-black p-3 sm:p-10 pt-20 sm:pt-28 flex flex-col justify-center items-center\">\r\n      {/* Back Button */}\r\n      <div className=\"flex w-full justify-start mb-8 mt-8 sm:mt-4 px-2 sm:px-4 md:px-8\">\r\n        <Link\r\n          href=\"/\"\r\n          className=\"inline-flex items-center px-4 py-3 rounded-lg text-gray-700 hover:text-black hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 touch-manipulation\"\r\n          style={{ WebkitTapHighlightColor: 'transparent' }}\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"h-5 w-5 mr-2\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            stroke=\"currentColor\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth={2}\r\n              d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\r\n            />\r\n          </svg>\r\n          Back to Home\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Search Bar and Sort Order */}\r\n      <div className=\"w-full max-w-4xl mt-4 sm:mt-8 px-2 sm:px-4 md:px-8\">\r\n        <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3\">\r\n          <div className=\"flex-1 w-full sm:max-w-[280px]\">\r\n            <div className=\"relative\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n                onBlur={handleSearch}\r\n                className=\"w-full py-2 px-4 bg-[#f1f1f1] rounded-full text-sm focus:outline-none\r\n                border border-transparent focus:border-gray-200 transition-all duration-300 pr-10\"\r\n              />\r\n              <div className=\"absolute right-3 top-1/2 -translate-y-1/2\">\r\n                <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Sort Order Selector */}\r\n          <select\r\n            value={sortOrder}\r\n            onChange={(e) => setSortOrder(e.target.value as 'newest' | 'oldest')}\r\n            className=\"px-3 py-2 rounded-lg text-sm bg-white border border-gray-200 focus:outline-none focus:ring-1 focus:ring-gray-300\"\r\n          >\r\n            <option value=\"newest\">Newest First</option>\r\n            <option value=\"oldest\">Oldest First</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Categories Section */}\r\n      <div className=\"w-full max-w-4xl mt-4 sm:mt-8 px-2 sm:px-4 md:px-8\">\r\n        <h3 className=\"text-sm sm:text-base font-semibold mb-2 sm:mb-4\">Categories</h3>\r\n        <div className=\"categories-section grid grid-cols-3 sm:grid-cols-6 gap-1.5 sm:gap-3\">\r\n          {CATEGORIES.map((category) => {\r\n            // Count products that match either by category ID or category name\r\n            const productCount = products.filter(p =>\r\n              p.category === category.id ||\r\n              p.categoryName === category.name\r\n            ).length;\r\n            return (\r\n              <motion.div\r\n                key={category.id}\r\n                data-category-id={category.id}\r\n                whileHover={{ scale: 1.03 }}\r\n                whileTap={{ scale: 0.97 }}\r\n                onClick={() => {\r\n                  const newCategory = activeCategory === category.id ? null : category.id;\r\n                  setActiveCategory(newCategory);\r\n\r\n                  if (typeof window !== 'undefined') {\r\n                    const url = new URL(window.location.href);\r\n                    if (newCategory) {\r\n                      url.searchParams.set('category', newCategory);\r\n                    } else {\r\n                      url.searchParams.delete('category');\r\n                    }\r\n                    window.history.pushState({}, '', url.toString());\r\n                  }\r\n                }}\r\n                className={`relative cursor-pointer p-2 sm:p-3 rounded-lg text-center transition-colors duration-200 group\r\n                  ${activeCategory === category.id\r\n                    ? 'bg-black text-white'\r\n                    : 'bg-gray-100 hover:bg-gray-200 text-gray-900'\r\n                  }\r\n                `}\r\n              >\r\n                <div className=\"text-xl sm:text-2xl mb-1\">{category.icon}</div>\r\n                <div className=\"text-[10px] sm:text-sm font-medium line-clamp-2\">\r\n                  {category.name}\r\n                </div>\r\n                {productCount > 0 && (\r\n                  <div className=\"text-[8px] sm:text-xs font-medium mt-1 bg-black/10 rounded-full px-1.5 py-0.5\">\r\n                    {productCount} items\r\n                  </div>\r\n                )}\r\n              </motion.div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* No Results Message */}\r\n      {filteredProducts.length === 0 && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"w-full text-center py-8 sm:py-20\"\r\n        >\r\n          <div className=\"text-2xl sm:text-4xl mb-2 sm:mb-4\">🔍</div>\r\n          <h3 className=\"text-lg sm:text-2xl font-semibold mb-1 sm:mb-2\">No products found</h3>\r\n          <p className=\"text-gray-600 text-xs sm:text-base\">\r\n            We couldn&apos;t find any products matching your search.\r\n            <br />\r\n            Try using different keywords or browsing our categories.\r\n          </p>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Products Grid */}\r\n      <div className=\"w-full max-w-7xl mt-8 sm:mt-12 px-2 sm:px-4 md:px-8\">\r\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4 place-items-center\">\r\n          {filteredProducts.map((product) => (\r\n            <ProductCard key={product.$id} product={product} />\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Scroll To Top Button */}\r\n      <AnimatePresence>\r\n        {showScrollTop && (\r\n          <motion.button\r\n            initial={{ opacity: 0, scale: 0.5 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            exit={{ opacity: 0, scale: 0.5 }}\r\n            onClick={scrollToTop}\r\n            className=\"fixed bottom-6 right-6 z-50 bg-black/80 hover:bg-black text-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg cursor-pointer backdrop-blur-sm transition-all duration-300 hover:shadow-xl\"\r\n            whileHover={{ scale: 1.1 }}\r\n            whileTap={{ scale: 0.9 }}\r\n          >\r\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\r\n            </svg>\r\n          </motion.button>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAAA;AAFA;;;;;;;;;;;AAuBe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,qBAAqB,eAAe,aAAa,GAAG,CAAC,aAAa,KAAK;IAC7E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAEhE,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,eAAe;YACnB,uCAAmC;;YAEnC;QACF;QAEA,QAAQ,iBAAiB,UAAU;QACnC,OAAO,IAAM,QAAQ,oBAAoB,UAAU;IACrD,GAAG;QAAC;KAAU;IAEd,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAEjC,MAAM,aAAa,aAAa,GAAG,CAAC;QACpC,MAAM,eAAe,aAAa,GAAG,CAAC,cAAc;QACpD,MAAM,cAAc,aAAa,GAAG,CAAC;QAErC,QAAQ,GAAG,CAAC,mBAAmB;YAAE;YAAY;YAAc;QAAY;QAEvE,IAAI,cAAc,cAAc;YAC9B,QAAQ,GAAG,CAAC,4BAA4B;YACxC,kBAAkB;YAElB,sCAAsC;YACtC,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;YACxC,IAAI,YAAY,CAAC,MAAM,CAAC;YACxB,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,IAAI,QAAQ;QAClD;QAEA,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,yBAAyB;YACrC,eAAe;QACjB;IACF,GAAG;QAAC;QAAc;KAAU;IAE5B,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBAEX,oDAAoD;gBACpD,MAAM,WAAW,MAAM,sHAAA,CAAA,YAAS,CAAC,aAAa,CAC5C,sHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,sHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;oBACE,8IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,8IAAA,CAAA,QAAK,CAAC,MAAM,CAAC;oBACb,8IAAA,CAAA,QAAK,CAAC,SAAS,CAAC,cAAc,0BAA0B;iBACzD;gBAGH,qDAAqD;gBACrD,IAAI,eAAe;uBAAI,SAAS,SAAS;iBAAC;gBAE1C,yEAAyE;gBACzE,IAAI,SAAS,KAAK,GAAG,KAAK;oBACxB,2CAA2C;oBAC3C,MAAM,gBAAgB,KAAK,IAAI,CAAC,SAAS,KAAK,GAAG;oBAEjD,gDAAgD;oBAChD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,MAAM,SAAS,IAAI;wBACnB,MAAM,qBAAqB,MAAM,sHAAA,CAAA,YAAS,CAAC,aAAa,CACtD,sHAAA,CAAA,iBAAc,CAAC,UAAU,EACzB,sHAAA,CAAA,iBAAc,CAAC,oBAAoB,EACnC;4BACE,8IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4BACZ,8IAAA,CAAA,QAAK,CAAC,MAAM,CAAC;4BACb,8IAAA,CAAA,QAAK,CAAC,SAAS,CAAC;yBACjB;wBAGH,mCAAmC;wBACnC,eAAe;+BAAI;+BAAiB,mBAAmB,SAAS;yBAAC;oBACnE;gBACF;gBAEA,QAAQ,GAAG,CAAC,2BAA2B,aAAa,MAAM,EAAE;gBAC5D,MAAM,eAAe;gBACrB,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA;YAC/B,2EAA2E;YAC3E,MAAM,kBAAkB,CAAC,kBACvB,QAAQ,QAAQ,KAAK,kBACrB,QAAQ,YAAY,KAAK,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,iBAAiB;YAE9E,sCAAsC;YACtC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC;uBACpB,EAAE,QAAQ,QAAQ,IAAI,OAAO;yBAC3B,EAAE,QAAQ,YAAY,IAAI,OAAO;2BAC/B,EAAE,eAAe;gCACZ,EAAE,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,iBAAiB,QAAQ,OAAO;4BACxE,EAAE,gBAAgB;MACxC,CAAC;YAED,MAAM,gBAAgB,CAAC,eACrB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YAEpE,OAAO,mBAAmB;QAC5B;QAEA,wBAAwB;QACxB,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,MAAM,QAAQ,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAC5C,MAAM,QAAQ,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAC5C,OAAO,cAAc,WAAW,QAAQ,QAAQ,QAAQ;QAC1D;QAEA,OAAO;IACT,GAAG;QAAC;QAAU;QAAgB;QAAa;KAAU;IAErD,iBAAiB;IACjB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,wCAAiD;;IAgBnD;IAEA,wBAAwB;IACxB,MAAM,cAAc;QAClB,wCAAiD;;IAEnD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,WAAU;sBAEV,cAAA,8OAAC,2IAAA,CAAA,UAAc;gBAAC,MAAK;gBAAQ,MAAK;;;;;;;;;;;IAGxC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;oBACV,OAAO;wBAAE,yBAAyB;oBAAc;;sCAEhD,8OAAC;4BACC,OAAM;4BACN,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,QAAO;sCAEP,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;wBAEA;;;;;;;;;;;;0BAMV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,QAAQ;wCACR,WAAU;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7E,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,8OAAC;oCAAO,OAAM;8CAAS;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAkD;;;;;;kCAChE,8OAAC;wBAAI,WAAU;kCACZ,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC;4BACf,mEAAmE;4BACnE,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,IACnC,EAAE,QAAQ,KAAK,SAAS,EAAE,IAC1B,EAAE,YAAY,KAAK,SAAS,IAAI,EAChC,MAAM;4BACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,oBAAkB,SAAS,EAAE;gCAC7B,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;oCACP,MAAM,cAAc,mBAAmB,SAAS,EAAE,GAAG,OAAO,SAAS,EAAE;oCACvE,kBAAkB;oCAElB,uCAAmC;;oCAQnC;gCACF;gCACA,WAAW,CAAC;kBACV,EAAE,mBAAmB,SAAS,EAAE,GAC5B,wBACA,8CACH;gBACH,CAAC;;kDAED,8OAAC;wCAAI,WAAU;kDAA4B,SAAS,IAAI;;;;;;kDACxD,8OAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI;;;;;;oCAEf,eAAe,mBACd,8OAAC;wCAAI,WAAU;;4CACZ;4CAAa;;;;;;;;+BA/Bb,SAAS,EAAE;;;;;wBAoCtB;;;;;;;;;;;;YAKH,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCAAoC;;;;;;kCACnD,8OAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAC/D,8OAAC;wBAAE,WAAU;;4BAAqC;0CAEhD,8OAAC;;;;;4BAAK;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,iIAAA,CAAA,cAAW;4BAAmB,SAAS;2BAAtB,QAAQ,GAAG;;;;;;;;;;;;;;;0BAMnC,8OAAC,yLAAA,CAAA,kBAAe;0BACb,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,SAAS;oBACT,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAI;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF"}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Code/Web%20Projects/Client%20Projects/Cepoka%20Website/cepoka/src/app/shop/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Suspense, useEffect } from 'react'\r\nimport <PERSON>Content from './ShopContent'\r\nimport SpinningLoader from '../Components/SpinningLoader'\r\n\r\nexport default function ShopPage() {\r\n  // Add effect to handle the category selection from URL hash\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      // Check if we have a hash parameter indicating we should select a category\r\n      if (window.location.hash === '#select-category') {\r\n        // Get the category from the URL\r\n        const urlParams = new URLSearchParams(window.location.search);\r\n        const categoryId = urlParams.get('category');\r\n\r\n        if (categoryId) {\r\n          console.log('Shop page: Found category in URL with select-category hash:', categoryId);\r\n\r\n          // Find the category element by ID\r\n          setTimeout(() => {\r\n            const categorySelector = `.categories-section [data-category-id=\"${categoryId}\"]`;\r\n            const categoryElement = document.querySelector(categorySelector);\r\n            if (categoryElement) {\r\n              (categoryElement as HTMLElement).click();\r\n            } else {\r\n              console.log('Could not find category element with selector:', categorySelector);\r\n            }\r\n          }, 500); // Give time for the categories to load\r\n        }\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <Suspense fallback={\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <SpinningLoader size=\"large\" text=\"Loading shop...\" />\r\n      </div>\r\n    }>\r\n      <ShopContent />\r\n    </Suspense>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAsBnC;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBACR,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,2IAAA,CAAA,UAAc;gBAAC,MAAK;gBAAQ,MAAK;;;;;;;;;;;kBAGpC,cAAA,8OAAC,kIAAA,CAAA,UAAW;;;;;;;;;;AAGlB"}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}