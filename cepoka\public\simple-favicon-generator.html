<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Favicon Generator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    canvas {
      border: 1px solid #ccc;
      margin: 10px 0;
      background-color: white;
    }
    .container {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    .btn {
      display: inline-block;
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      margin-top: 10px;
    }
    .instructions {
      background-color: #fffde7;
      padding: 15px;
      border-left: 4px solid #ffd600;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>Simple Favicon Generator</h1>
  
  <div class="container">
    <h2>Original Logo</h2>
    <img src="/icons/sitelogo.png" width="100" height="100" alt="Original Logo" id="originalLogo">
  </div>
  
  <div class="container">
    <h2>Favicon with White Background</h2>
    <canvas id="faviconCanvas" width="32" height="32"></canvas>
    <br>
    <a href="#" id="downloadLink" class="btn">Download favicon.ico</a>
  </div>
  
  <div class="instructions">
    <h3>Instructions:</h3>
    <ol>
      <li>Click the "Download favicon.ico" button</li>
      <li>Save the file to your computer</li>
      <li>Place the downloaded file in the <code>/public</code> directory of your project</li>
      <li>The file should be named <code>favicon.ico</code></li>
    </ol>
  </div>
  
  <script>
    window.onload = function() {
      const canvas = document.getElementById('faviconCanvas');
      const ctx = canvas.getContext('2d');
      
      // Draw white background
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Load and draw the logo
      const img = document.getElementById('originalLogo');
      
      // Draw the logo centered in the canvas
      const size = Math.min(canvas.width, canvas.height) * 0.8; // 80% of canvas size
      const x = (canvas.width - size) / 2;
      const y = (canvas.height - size) / 2;
      ctx.drawImage(img, x, y, size, size);
      
      // Set up download link
      const downloadLink = document.getElementById('downloadLink');
      downloadLink.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Convert canvas to blob
        canvas.toBlob(function(blob) {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'favicon.ico';
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        });
      });
    };
  </script>
</body>
</html>
